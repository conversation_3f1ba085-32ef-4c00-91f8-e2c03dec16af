import requests
import os
from flask import Blueprint, request, jsonify
from flask_cors import cross_origin
from epub2pdf import epub2pdf
import json

from src.routes.llm import extract_book_info # Import the LLM extraction function
from src.routes.arabic_books import search_aco # Import Arabic Collections Online search

enhanced_book_bp = Blueprint("enhanced_book", __name__)

GOOGLE_BOOKS_API = "https://www.googleapis.com/books/v1/volumes"
GUTENDEX_API = "https://gutendex.com/books"

# Helper function to get PDF URL from Google Books API response
def get_google_books_pdf_url(access_info):
    if access_info and access_info.get("viewability") == "FULL" and access_info.get("pdf") and access_info["pdf"].get("isAvailable"):
        return access_info["pdf"].get("acsTokenLink") or access_info["pdf"].get("downloadLink")
    return None

# Helper function to get PDF URL from Gutendex API response
def get_gutendex_pdf_url(formats):
    if formats:
        # Prioritize application/pdf, then text/html, then text/plain
        if "application/pdf" in formats:
            return formats["application/pdf"]
        elif "text/html" in formats:
            return formats["text/html"]
        elif "text/plain" in formats:
            return formats["text/plain"]
    return None

# Helper function to get PDF URL from Internet Archive
def get_internet_archive_pdf_url(identifier):
    if identifier:
        return f"https://archive.org/download/{identifier}/{identifier}.pdf"
    return None

@enhanced_book_bp.route("/enhanced-search", methods=["POST"])
@cross_origin()
def enhanced_search():
    try:
        data = request.get_json()
        query = data.get("query")
        lang = data.get("lang", "en") # Default to English

        if not query:
            return jsonify({"error": "Query is required"}), 400

        # Step 1: Use LLM to extract structured information from the query
        llm_extracted_info = extract_book_info(query)
        llm_title = llm_extracted_info.get("title")
        llm_author = llm_extracted_info.get("author")
        llm_categories = llm_extracted_info.get("categories", [])

        # Determine the primary search term for external APIs
        primary_search_term = llm_title or query

        # Initialize results list
        books_data = []

        # --- Search Google Books (for general info, cover, categories) ---
        google_params = {"q": primary_search_term, "langRestrict": lang}
        if llm_author: # Add author to Google Books search if available from LLM
            google_params["q"] += f" author:{llm_author}"

        google_response = requests.get(GOOGLE_BOOKS_API, params=google_params)
        google_response.raise_for_status()
        google_data = google_response.json()

        for item in google_data.get("items", [])[:5]: # Limit to top 5 results from Google
            volume_info = item.get("volumeInfo", {})
            access_info = item.get("accessInfo", {})

            title = volume_info.get("title")
            authors = volume_info.get("authors", [])
            categories = volume_info.get("categories", [])
            description = volume_info.get("description")
            image_links = volume_info.get("imageLinks", {})
            thumbnail = image_links.get("thumbnail")
            info_link = volume_info.get("infoLink")
            
            pdf_url = get_google_books_pdf_url(access_info)

            books_data.append({
                "title": title,
                "author": ", ".join(authors),
                "categories": categories,
                "description": description,
                "thumbnail": thumbnail,
                "info_link": info_link,
                "pdf_links": [{"source": "Google Books", "url": pdf_url}] if pdf_url else []
            })

        # --- Search Gutendex (for public domain PDFs) ---
        gutendex_params = {"search": primary_search_term}
        gutendex_response = requests.get(GUTENDEX_API, params=gutendex_params)
        gutendex_response.raise_for_status()
        gutendex_data = gutendex_response.json()

        for result in gutendex_data.get("results", [])[:5]: # Limit to top 5 results from Gutendex
            pdf_url = get_gutendex_pdf_url(result.get("formats"))
            if pdf_url:
                # Check if this book is already in books_data (by title/author)
                found = False
                for book in books_data:
                    if book["title"] == result.get("title") and book["author"] == ", ".join(result.get("authors", [])):
                        book["pdf_links"].append({"source": "Gutendex", "url": pdf_url})
                        found = True
                        break
                if not found:
                    books_data.append({
                        "title": result.get("title"),
                        "author": ", ".join(result.get("authors", [])),
                        "categories": result.get("subjects", []),
                        "description": "", # Gutendex doesn't provide descriptions
                        "thumbnail": None, # Gutendex doesn't provide thumbnails directly
                        "info_link": result.get("formats", {}).get("text/html"), # Use HTML link as info link
                        "pdf_links": [{"source": "Gutendex", "url": pdf_url}]
                    })

        # --- Search Arabic Collections Online (ACO) for Arabic books ---
        # Prioritize ACO if the query is in Arabic or LLM suggests Arabic book
        if lang == "ar" or any(c in "أب ت ث ج ح خ د ذ ر ز س ش ص ض ط ظ ع غ ف ق ك ل م ن ه و ي" for c in query) or (llm_title and any(c in "أب ت ث ج ح خ د ذ ر ز س ش ص ض ط ظ ع غ ف ق ك ل م ن ه و ي" for c in llm_title)):
            aco_results = search_aco(query)
            for aco_book in aco_results:
                # Check if this book is already in books_data
                found = False
                for book in books_data:
                    if book["title"] == aco_book["title"] and book["author"] == aco_book["author"]:
                        book["pdf_links"].extend(aco_book["pdf_links"])
                        found = True
                        break
                if not found:
                    books_data.append({
                        "title": aco_book["title"],
                        "author": aco_book["author"],
                        "categories": [], # ACO doesn't provide categories in this format
                        "description": "",
                        "thumbnail": None,
                        "info_link": None,
                        "pdf_links": aco_book["pdf_links"]
                    })

        # --- LLM for related books and enhanced descriptions (optional, can be moved to frontend or separate endpoint) ---
        # For now, we'll keep it simple and focus on search results.
        # The /related-books endpoint in llm.py can be called separately by the frontend.

        return jsonify({"results": books_data})

    except requests.exceptions.RequestException as e:
        print(f"Error during enhanced search: {e}")
        return jsonify({"error": f"External API error: {e}"}), 500
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return jsonify({"error": "Internal server error"}), 500

@enhanced_book_bp.route("/convert-to-pdf", methods=["POST"])
@cross_origin()
def convert_to_pdf():
    try:
        data = request.get_json()
        file_url = data.get("file_url")
        output_filename = data.get("output_filename", "converted_book.pdf")

        if not file_url:
            return jsonify({"error": "File URL is required"}), 400

        # Download the file first
        response = requests.get(file_url, stream=True)
        response.raise_for_status()

        input_path = f"/tmp/{os.path.basename(file_url)}"
        with open(input_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        output_path = f"/tmp/{output_filename}"
        epub2pdf(input_path, output_path)

        # For now, we'll just return a success message. In a real deployment,
        # you'd need to serve this PDF or provide a temporary download link.
        # For Vercel, you might need to upload it to a storage service.
        return jsonify({"message": "Conversion successful", "download_path": output_path})

    except Exception as e:
        print(f"Error during PDF conversion: {e}")
        return jsonify({"error": "PDF conversion failed"}), 500


# Helper function to get PDF URL from Internet Archive (if needed, can be integrated into enhanced_search)
# def get_internet_archive_pdf_url(identifier):
#     if identifier:
#         return f"https://archive.org/download/{identifier}/{identifier}.pdf"
#     return None

# You might need to add a dedicated Internet Archive search function if their API is complex
# For now, relying on Google Books and Gutendex to link to IA where applicable. 
# ACO is specifically for Arabic books.


