import requests
from bs4 import BeautifulSoup

def search_aco(query):
    search_url = f"https://dlib.nyu.edu/aco/search/?q={query}&scope=containsAny"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    try:
        response = requests.get(search_url, headers=headers)
        response.raise_for_status()  # Raise an HTTPError for bad responses (4xx or 5xx)
        soup = BeautifulSoup(response.text, "html.parser")
        
        results = []
        # Find all book result containers - updated selector
        book_containers = soup.find_all("div", class_="item-details") 

        for container in book_containers:
            title_tag = container.find("h3", class_="item-title") # Updated selector
            author_tag = container.find("p", class_="item-author") # Updated selector
            
            title = title_tag.text.strip() if title_tag else "N/A"
            author = author_tag.text.strip() if author_tag else "N/A"
            
            pdf_links = []
            # Find PDF download links within each container - updated selectors
            low_res_pdf = container.find("a", class_="download-link", string="تحميل دِقّة منخفضة Low-resolution PDF")
            high_res_pdf = container.find("a", class_="download-link", string="تحميل دِقّة عالية High-resolution PDF")

            if low_res_pdf and low_res_pdf.get("href"):
                pdf_links.append({"type": "low_res_pdf", "url": low_res_pdf.get("href")})
            if high_res_pdf and high_res_pdf.get("href"):
                pdf_links.append({"type": "high_res_pdf", "url": high_res_pdf.get("href")})
            
            if pdf_links:
                results.append({
                    "title": title,
                    "author": author,
                    "pdf_links": pdf_links
                })
        return results
    except requests.exceptions.RequestException as e:
        print(f"Error searching ACO: {e}")
        return []
    except Exception as e:
        print(f"An unexpected error occurred during ACO search: {e}")
        return []

# Example usage (for testing purposes)
if __name__ == "__main__":
    # This part will only run when the script is executed directly
    # not when imported as a module
    arabic_books = search_aco("أليس في بلاد العجائب")
    for book in arabic_books:
        print(f"Title: {book.get("title")}")
        print(f"Author: {book.get("author")}")
        for pdf in book.get("pdf_links", []):
            print(f"  PDF ({pdf.get("type")}): {pdf.get("url")}")
        print("---")


