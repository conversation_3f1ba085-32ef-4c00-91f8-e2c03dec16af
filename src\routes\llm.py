import os
from flask import Blueprint, request, jsonify
from flask_cors import cross_origin
from groq import Groq

llm_bp = Blueprint("llm", __name__)

# Initialize Groq client with API key from environment variable
# It's safer to use environment variables for API keys in production
# For local testing, you can directly put your key here, but remove it before committing to public repo
GROQ_API_KEY = os.environ.get("GROQ_API_KEY", "********************************************************")
client = Groq(api_key=GROQ_API_KEY)

@llm_bp.route("/chat", methods=["POST"])
@cross_origin()
def chat():
    try:
        data = request.get_json()
        user_message = data.get("message")
        
        if not user_message:
            return jsonify({"error": "Message is required"}), 400

        chat_completion = client.chat.completions.create(
            messages=[
                {
                    "role": "user",
                    "content": user_message,
                }
            ],
            model="llama3-8b-8192", # You can choose other models like "mixtral-8x7b-32768" or "llama3-70b-8192"
        )

        llm_response = chat_completion.choices[0].message.content
        return jsonify({"response": llm_response})

    except Exception as e:
        print(f"Error in LLM chat: {e}")
        return jsonify({"error": "Internal server error"}), 500

@llm_bp.route("/related-books", methods=["POST"])
@cross_origin()
def related_books():
    try:
        data = request.get_json()
        book_title = data.get("title")
        book_author = data.get("author")
        
        if not book_title:
            return jsonify({"error": "Book title is required"}), 400

        prompt = f"Suggest 3-5 books similar to \'{book_title}\' by {book_author if book_author else 'an unknown author'}. Provide only the book titles and authors, one per line, in the format: Title - Author."

        chat_completion = client.chat.completions.create(
            messages=[
                {
                    "role": "user",
                    "content": prompt,
                }
            ],
            model="llama3-8b-8192",
        )

        llm_response = chat_completion.choices[0].message.content
        
        # Parse the response into a list of dictionaries
        related_books_list = []
        for line in llm_response.split("\n"):
            if " - " in line:
                parts = line.split(" - ", 1)
                if len(parts) == 2:
                    related_books_list.append({"title": parts[0].strip(), "author": parts[1].strip()})
        
        return jsonify({"related_books": related_books_list})

    except Exception as e:
        print(f"Error in related books suggestion: {e}")
        return jsonify({"error": "Internal server error"}), 500


