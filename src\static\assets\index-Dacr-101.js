(function(){const p=document.createElement("link").relList;if(p&&p.supports&&p.supports("modulepreload"))return;for(const T of document.querySelectorAll('link[rel="modulepreload"]'))s(T);new MutationObserver(T=>{for(const O of T)if(O.type==="childList")for(const U of O.addedNodes)U.tagName==="LINK"&&U.rel==="modulepreload"&&s(U)}).observe(document,{childList:!0,subtree:!0});function y(T){const O={};return T.integrity&&(O.integrity=T.integrity),T.referrerPolicy&&(O.referrerPolicy=T.referrerPolicy),T.crossOrigin==="use-credentials"?O.credentials="include":T.crossOrigin==="anonymous"?O.credentials="omit":O.credentials="same-origin",O}function s(T){if(T.ep)return;T.ep=!0;const O=y(T);fetch(T.href,O)}})();var df={exports:{}},Nu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sd;function T0(){if(Sd)return Nu;Sd=1;var f=Symbol.for("react.transitional.element"),p=Symbol.for("react.fragment");function y(s,T,O){var U=null;if(O!==void 0&&(U=""+O),T.key!==void 0&&(U=""+T.key),"key"in T){O={};for(var w in T)w!=="key"&&(O[w]=T[w])}else O=T;return T=O.ref,{$$typeof:f,type:s,key:U,ref:T!==void 0?T:null,props:O}}return Nu.Fragment=p,Nu.jsx=y,Nu.jsxs=y,Nu}var xd;function A0(){return xd||(xd=1,df.exports=T0()),df.exports}var C=A0(),mf={exports:{}},F={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Td;function E0(){if(Td)return F;Td=1;var f=Symbol.for("react.transitional.element"),p=Symbol.for("react.portal"),y=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),T=Symbol.for("react.profiler"),O=Symbol.for("react.consumer"),U=Symbol.for("react.context"),w=Symbol.for("react.forward_ref"),_=Symbol.for("react.suspense"),S=Symbol.for("react.memo"),R=Symbol.for("react.lazy"),J=Symbol.iterator;function $(o){return o===null||typeof o!="object"?null:(o=J&&o[J]||o["@@iterator"],typeof o=="function"?o:null)}var xl={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Z=Object.assign,W={};function nl(o,M,j){this.props=o,this.context=M,this.refs=W,this.updater=j||xl}nl.prototype.isReactComponent={},nl.prototype.setState=function(o,M){if(typeof o!="object"&&typeof o!="function"&&o!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,o,M,"setState")},nl.prototype.forceUpdate=function(o){this.updater.enqueueForceUpdate(this,o,"forceUpdate")};function Zl(){}Zl.prototype=nl.prototype;function Vl(o,M,j){this.props=o,this.context=M,this.refs=W,this.updater=j||xl}var ml=Vl.prototype=new Zl;ml.constructor=Vl,Z(ml,nl.prototype),ml.isPureReactComponent=!0;var Yl=Array.isArray,P={H:null,A:null,T:null,S:null,V:null},Ml=Object.prototype.hasOwnProperty;function jl(o,M,j,N,q,el){return j=el.ref,{$$typeof:f,type:o,key:M,ref:j!==void 0?j:null,props:el}}function Q(o,M){return jl(o.type,M,void 0,void 0,void 0,o.props)}function Nl(o){return typeof o=="object"&&o!==null&&o.$$typeof===f}function jt(o){var M={"=":"=0",":":"=2"};return"$"+o.replace(/[=:]/g,function(j){return M[j]})}var mt=/\/+/g;function Ol(o,M){return typeof o=="object"&&o!==null&&o.key!=null?jt(""+o.key):M.toString(36)}function Mt(){}function At(o){switch(o.status){case"fulfilled":return o.value;case"rejected":throw o.reason;default:switch(typeof o.status=="string"?o.then(Mt,Mt):(o.status="pending",o.then(function(M){o.status==="pending"&&(o.status="fulfilled",o.value=M)},function(M){o.status==="pending"&&(o.status="rejected",o.reason=M)})),o.status){case"fulfilled":return o.value;case"rejected":throw o.reason}}throw o}function Al(o,M,j,N,q){var el=typeof o;(el==="undefined"||el==="boolean")&&(o=null);var k=!1;if(o===null)k=!0;else switch(el){case"bigint":case"string":case"number":k=!0;break;case"object":switch(o.$$typeof){case f:case p:k=!0;break;case R:return k=o._init,Al(k(o._payload),M,j,N,q)}}if(k)return q=q(o),k=N===""?"."+Ol(o,0):N,Yl(q)?(j="",k!=null&&(j=k.replace(mt,"$&/")+"/"),Al(q,M,j,"",function(at){return at})):q!=null&&(Nl(q)&&(q=Q(q,j+(q.key==null||o&&o.key===q.key?"":(""+q.key).replace(mt,"$&/")+"/")+k)),M.push(q)),1;k=0;var cl=N===""?".":N+":";if(Yl(o))for(var gl=0;gl<o.length;gl++)N=o[gl],el=cl+Ol(N,gl),k+=Al(N,M,j,el,q);else if(gl=$(o),typeof gl=="function")for(o=gl.call(o),gl=0;!(N=o.next()).done;)N=N.value,el=cl+Ol(N,gl++),k+=Al(N,M,j,el,q);else if(el==="object"){if(typeof o.then=="function")return Al(At(o),M,j,N,q);throw M=String(o),Error("Objects are not valid as a React child (found: "+(M==="[object Object]"?"object with keys {"+Object.keys(o).join(", ")+"}":M)+"). If you meant to render a collection of children, use an array instead.")}return k}function x(o,M,j){if(o==null)return o;var N=[],q=0;return Al(o,N,"","",function(el){return M.call(j,el,q++)}),N}function H(o){if(o._status===-1){var M=o._result;M=M(),M.then(function(j){(o._status===0||o._status===-1)&&(o._status=1,o._result=j)},function(j){(o._status===0||o._status===-1)&&(o._status=2,o._result=j)}),o._status===-1&&(o._status=0,o._result=M)}if(o._status===1)return o._result.default;throw o._result}var D=typeof reportError=="function"?reportError:function(o){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var M=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof o=="object"&&o!==null&&typeof o.message=="string"?String(o.message):String(o),error:o});if(!window.dispatchEvent(M))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",o);return}console.error(o)};function rl(){}return F.Children={map:x,forEach:function(o,M,j){x(o,function(){M.apply(this,arguments)},j)},count:function(o){var M=0;return x(o,function(){M++}),M},toArray:function(o){return x(o,function(M){return M})||[]},only:function(o){if(!Nl(o))throw Error("React.Children.only expected to receive a single React element child.");return o}},F.Component=nl,F.Fragment=y,F.Profiler=T,F.PureComponent=Vl,F.StrictMode=s,F.Suspense=_,F.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=P,F.__COMPILER_RUNTIME={__proto__:null,c:function(o){return P.H.useMemoCache(o)}},F.cache=function(o){return function(){return o.apply(null,arguments)}},F.cloneElement=function(o,M,j){if(o==null)throw Error("The argument must be a React element, but you passed "+o+".");var N=Z({},o.props),q=o.key,el=void 0;if(M!=null)for(k in M.ref!==void 0&&(el=void 0),M.key!==void 0&&(q=""+M.key),M)!Ml.call(M,k)||k==="key"||k==="__self"||k==="__source"||k==="ref"&&M.ref===void 0||(N[k]=M[k]);var k=arguments.length-2;if(k===1)N.children=j;else if(1<k){for(var cl=Array(k),gl=0;gl<k;gl++)cl[gl]=arguments[gl+2];N.children=cl}return jl(o.type,q,void 0,void 0,el,N)},F.createContext=function(o){return o={$$typeof:U,_currentValue:o,_currentValue2:o,_threadCount:0,Provider:null,Consumer:null},o.Provider=o,o.Consumer={$$typeof:O,_context:o},o},F.createElement=function(o,M,j){var N,q={},el=null;if(M!=null)for(N in M.key!==void 0&&(el=""+M.key),M)Ml.call(M,N)&&N!=="key"&&N!=="__self"&&N!=="__source"&&(q[N]=M[N]);var k=arguments.length-2;if(k===1)q.children=j;else if(1<k){for(var cl=Array(k),gl=0;gl<k;gl++)cl[gl]=arguments[gl+2];q.children=cl}if(o&&o.defaultProps)for(N in k=o.defaultProps,k)q[N]===void 0&&(q[N]=k[N]);return jl(o,el,void 0,void 0,null,q)},F.createRef=function(){return{current:null}},F.forwardRef=function(o){return{$$typeof:w,render:o}},F.isValidElement=Nl,F.lazy=function(o){return{$$typeof:R,_payload:{_status:-1,_result:o},_init:H}},F.memo=function(o,M){return{$$typeof:S,type:o,compare:M===void 0?null:M}},F.startTransition=function(o){var M=P.T,j={};P.T=j;try{var N=o(),q=P.S;q!==null&&q(j,N),typeof N=="object"&&N!==null&&typeof N.then=="function"&&N.then(rl,D)}catch(el){D(el)}finally{P.T=M}},F.unstable_useCacheRefresh=function(){return P.H.useCacheRefresh()},F.use=function(o){return P.H.use(o)},F.useActionState=function(o,M,j){return P.H.useActionState(o,M,j)},F.useCallback=function(o,M){return P.H.useCallback(o,M)},F.useContext=function(o){return P.H.useContext(o)},F.useDebugValue=function(){},F.useDeferredValue=function(o,M){return P.H.useDeferredValue(o,M)},F.useEffect=function(o,M,j){var N=P.H;if(typeof j=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return N.useEffect(o,M)},F.useId=function(){return P.H.useId()},F.useImperativeHandle=function(o,M,j){return P.H.useImperativeHandle(o,M,j)},F.useInsertionEffect=function(o,M){return P.H.useInsertionEffect(o,M)},F.useLayoutEffect=function(o,M){return P.H.useLayoutEffect(o,M)},F.useMemo=function(o,M){return P.H.useMemo(o,M)},F.useOptimistic=function(o,M){return P.H.useOptimistic(o,M)},F.useReducer=function(o,M,j){return P.H.useReducer(o,M,j)},F.useRef=function(o){return P.H.useRef(o)},F.useState=function(o){return P.H.useState(o)},F.useSyncExternalStore=function(o,M,j){return P.H.useSyncExternalStore(o,M,j)},F.useTransition=function(){return P.H.useTransition()},F.version="19.1.0",F}var Ad;function zf(){return Ad||(Ad=1,mf.exports=E0()),mf.exports}var pl=zf(),hf={exports:{}},Uu={},vf={exports:{}},yf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ed;function z0(){return Ed||(Ed=1,function(f){function p(x,H){var D=x.length;x.push(H);l:for(;0<D;){var rl=D-1>>>1,o=x[rl];if(0<T(o,H))x[rl]=H,x[D]=o,D=rl;else break l}}function y(x){return x.length===0?null:x[0]}function s(x){if(x.length===0)return null;var H=x[0],D=x.pop();if(D!==H){x[0]=D;l:for(var rl=0,o=x.length,M=o>>>1;rl<M;){var j=2*(rl+1)-1,N=x[j],q=j+1,el=x[q];if(0>T(N,D))q<o&&0>T(el,N)?(x[rl]=el,x[q]=D,rl=q):(x[rl]=N,x[j]=D,rl=j);else if(q<o&&0>T(el,D))x[rl]=el,x[q]=D,rl=q;else break l}}return H}function T(x,H){var D=x.sortIndex-H.sortIndex;return D!==0?D:x.id-H.id}if(f.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var O=performance;f.unstable_now=function(){return O.now()}}else{var U=Date,w=U.now();f.unstable_now=function(){return U.now()-w}}var _=[],S=[],R=1,J=null,$=3,xl=!1,Z=!1,W=!1,nl=!1,Zl=typeof setTimeout=="function"?setTimeout:null,Vl=typeof clearTimeout=="function"?clearTimeout:null,ml=typeof setImmediate<"u"?setImmediate:null;function Yl(x){for(var H=y(S);H!==null;){if(H.callback===null)s(S);else if(H.startTime<=x)s(S),H.sortIndex=H.expirationTime,p(_,H);else break;H=y(S)}}function P(x){if(W=!1,Yl(x),!Z)if(y(_)!==null)Z=!0,Ml||(Ml=!0,Ol());else{var H=y(S);H!==null&&Al(P,H.startTime-x)}}var Ml=!1,jl=-1,Q=5,Nl=-1;function jt(){return nl?!0:!(f.unstable_now()-Nl<Q)}function mt(){if(nl=!1,Ml){var x=f.unstable_now();Nl=x;var H=!0;try{l:{Z=!1,W&&(W=!1,Vl(jl),jl=-1),xl=!0;var D=$;try{t:{for(Yl(x),J=y(_);J!==null&&!(J.expirationTime>x&&jt());){var rl=J.callback;if(typeof rl=="function"){J.callback=null,$=J.priorityLevel;var o=rl(J.expirationTime<=x);if(x=f.unstable_now(),typeof o=="function"){J.callback=o,Yl(x),H=!0;break t}J===y(_)&&s(_),Yl(x)}else s(_);J=y(_)}if(J!==null)H=!0;else{var M=y(S);M!==null&&Al(P,M.startTime-x),H=!1}}break l}finally{J=null,$=D,xl=!1}H=void 0}}finally{H?Ol():Ml=!1}}}var Ol;if(typeof ml=="function")Ol=function(){ml(mt)};else if(typeof MessageChannel<"u"){var Mt=new MessageChannel,At=Mt.port2;Mt.port1.onmessage=mt,Ol=function(){At.postMessage(null)}}else Ol=function(){Zl(mt,0)};function Al(x,H){jl=Zl(function(){x(f.unstable_now())},H)}f.unstable_IdlePriority=5,f.unstable_ImmediatePriority=1,f.unstable_LowPriority=4,f.unstable_NormalPriority=3,f.unstable_Profiling=null,f.unstable_UserBlockingPriority=2,f.unstable_cancelCallback=function(x){x.callback=null},f.unstable_forceFrameRate=function(x){0>x||125<x?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Q=0<x?Math.floor(1e3/x):5},f.unstable_getCurrentPriorityLevel=function(){return $},f.unstable_next=function(x){switch($){case 1:case 2:case 3:var H=3;break;default:H=$}var D=$;$=H;try{return x()}finally{$=D}},f.unstable_requestPaint=function(){nl=!0},f.unstable_runWithPriority=function(x,H){switch(x){case 1:case 2:case 3:case 4:case 5:break;default:x=3}var D=$;$=x;try{return H()}finally{$=D}},f.unstable_scheduleCallback=function(x,H,D){var rl=f.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?rl+D:rl):D=rl,x){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return o=D+o,x={id:R++,callback:H,priorityLevel:x,startTime:D,expirationTime:o,sortIndex:-1},D>rl?(x.sortIndex=D,p(S,x),y(_)===null&&x===y(S)&&(W?(Vl(jl),jl=-1):W=!0,Al(P,D-rl))):(x.sortIndex=o,p(_,x),Z||xl||(Z=!0,Ml||(Ml=!0,Ol()))),x},f.unstable_shouldYield=jt,f.unstable_wrapCallback=function(x){var H=$;return function(){var D=$;$=H;try{return x.apply(this,arguments)}finally{$=D}}}}(yf)),yf}var zd;function _0(){return zd||(zd=1,vf.exports=z0()),vf.exports}var gf={exports:{}},$l={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _d;function M0(){if(_d)return $l;_d=1;var f=zf();function p(_){var S="https://react.dev/errors/"+_;if(1<arguments.length){S+="?args[]="+encodeURIComponent(arguments[1]);for(var R=2;R<arguments.length;R++)S+="&args[]="+encodeURIComponent(arguments[R])}return"Minified React error #"+_+"; visit "+S+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function y(){}var s={d:{f:y,r:function(){throw Error(p(522))},D:y,C:y,L:y,m:y,X:y,S:y,M:y},p:0,findDOMNode:null},T=Symbol.for("react.portal");function O(_,S,R){var J=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:T,key:J==null?null:""+J,children:_,containerInfo:S,implementation:R}}var U=f.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function w(_,S){if(_==="font")return"";if(typeof S=="string")return S==="use-credentials"?S:""}return $l.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,$l.createPortal=function(_,S){var R=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!S||S.nodeType!==1&&S.nodeType!==9&&S.nodeType!==11)throw Error(p(299));return O(_,S,null,R)},$l.flushSync=function(_){var S=U.T,R=s.p;try{if(U.T=null,s.p=2,_)return _()}finally{U.T=S,s.p=R,s.d.f()}},$l.preconnect=function(_,S){typeof _=="string"&&(S?(S=S.crossOrigin,S=typeof S=="string"?S==="use-credentials"?S:"":void 0):S=null,s.d.C(_,S))},$l.prefetchDNS=function(_){typeof _=="string"&&s.d.D(_)},$l.preinit=function(_,S){if(typeof _=="string"&&S&&typeof S.as=="string"){var R=S.as,J=w(R,S.crossOrigin),$=typeof S.integrity=="string"?S.integrity:void 0,xl=typeof S.fetchPriority=="string"?S.fetchPriority:void 0;R==="style"?s.d.S(_,typeof S.precedence=="string"?S.precedence:void 0,{crossOrigin:J,integrity:$,fetchPriority:xl}):R==="script"&&s.d.X(_,{crossOrigin:J,integrity:$,fetchPriority:xl,nonce:typeof S.nonce=="string"?S.nonce:void 0})}},$l.preinitModule=function(_,S){if(typeof _=="string")if(typeof S=="object"&&S!==null){if(S.as==null||S.as==="script"){var R=w(S.as,S.crossOrigin);s.d.M(_,{crossOrigin:R,integrity:typeof S.integrity=="string"?S.integrity:void 0,nonce:typeof S.nonce=="string"?S.nonce:void 0})}}else S==null&&s.d.M(_)},$l.preload=function(_,S){if(typeof _=="string"&&typeof S=="object"&&S!==null&&typeof S.as=="string"){var R=S.as,J=w(R,S.crossOrigin);s.d.L(_,R,{crossOrigin:J,integrity:typeof S.integrity=="string"?S.integrity:void 0,nonce:typeof S.nonce=="string"?S.nonce:void 0,type:typeof S.type=="string"?S.type:void 0,fetchPriority:typeof S.fetchPriority=="string"?S.fetchPriority:void 0,referrerPolicy:typeof S.referrerPolicy=="string"?S.referrerPolicy:void 0,imageSrcSet:typeof S.imageSrcSet=="string"?S.imageSrcSet:void 0,imageSizes:typeof S.imageSizes=="string"?S.imageSizes:void 0,media:typeof S.media=="string"?S.media:void 0})}},$l.preloadModule=function(_,S){if(typeof _=="string")if(S){var R=w(S.as,S.crossOrigin);s.d.m(_,{as:typeof S.as=="string"&&S.as!=="script"?S.as:void 0,crossOrigin:R,integrity:typeof S.integrity=="string"?S.integrity:void 0})}else s.d.m(_)},$l.requestFormReset=function(_){s.d.r(_)},$l.unstable_batchedUpdates=function(_,S){return _(S)},$l.useFormState=function(_,S,R){return U.H.useFormState(_,S,R)},$l.useFormStatus=function(){return U.H.useHostTransitionStatus()},$l.version="19.1.0",$l}var Md;function O0(){if(Md)return gf.exports;Md=1;function f(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(f)}catch(p){console.error(p)}}return f(),gf.exports=M0(),gf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Od;function D0(){if(Od)return Uu;Od=1;var f=_0(),p=zf(),y=O0();function s(l){var t="https://react.dev/errors/"+l;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var e=2;e<arguments.length;e++)t+="&args[]="+encodeURIComponent(arguments[e])}return"Minified React error #"+l+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function T(l){return!(!l||l.nodeType!==1&&l.nodeType!==9&&l.nodeType!==11)}function O(l){var t=l,e=l;if(l.alternate)for(;t.return;)t=t.return;else{l=t;do t=l,(t.flags&4098)!==0&&(e=t.return),l=t.return;while(l)}return t.tag===3?e:null}function U(l){if(l.tag===13){var t=l.memoizedState;if(t===null&&(l=l.alternate,l!==null&&(t=l.memoizedState)),t!==null)return t.dehydrated}return null}function w(l){if(O(l)!==l)throw Error(s(188))}function _(l){var t=l.alternate;if(!t){if(t=O(l),t===null)throw Error(s(188));return t!==l?null:l}for(var e=l,a=t;;){var u=e.return;if(u===null)break;var n=u.alternate;if(n===null){if(a=u.return,a!==null){e=a;continue}break}if(u.child===n.child){for(n=u.child;n;){if(n===e)return w(u),l;if(n===a)return w(u),t;n=n.sibling}throw Error(s(188))}if(e.return!==a.return)e=u,a=n;else{for(var i=!1,c=u.child;c;){if(c===e){i=!0,e=u,a=n;break}if(c===a){i=!0,a=u,e=n;break}c=c.sibling}if(!i){for(c=n.child;c;){if(c===e){i=!0,e=n,a=u;break}if(c===a){i=!0,a=n,e=u;break}c=c.sibling}if(!i)throw Error(s(189))}}if(e.alternate!==a)throw Error(s(190))}if(e.tag!==3)throw Error(s(188));return e.stateNode.current===e?l:t}function S(l){var t=l.tag;if(t===5||t===26||t===27||t===6)return l;for(l=l.child;l!==null;){if(t=S(l),t!==null)return t;l=l.sibling}return null}var R=Object.assign,J=Symbol.for("react.element"),$=Symbol.for("react.transitional.element"),xl=Symbol.for("react.portal"),Z=Symbol.for("react.fragment"),W=Symbol.for("react.strict_mode"),nl=Symbol.for("react.profiler"),Zl=Symbol.for("react.provider"),Vl=Symbol.for("react.consumer"),ml=Symbol.for("react.context"),Yl=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),Ml=Symbol.for("react.suspense_list"),jl=Symbol.for("react.memo"),Q=Symbol.for("react.lazy"),Nl=Symbol.for("react.activity"),jt=Symbol.for("react.memo_cache_sentinel"),mt=Symbol.iterator;function Ol(l){return l===null||typeof l!="object"?null:(l=mt&&l[mt]||l["@@iterator"],typeof l=="function"?l:null)}var Mt=Symbol.for("react.client.reference");function At(l){if(l==null)return null;if(typeof l=="function")return l.$$typeof===Mt?null:l.displayName||l.name||null;if(typeof l=="string")return l;switch(l){case Z:return"Fragment";case nl:return"Profiler";case W:return"StrictMode";case P:return"Suspense";case Ml:return"SuspenseList";case Nl:return"Activity"}if(typeof l=="object")switch(l.$$typeof){case xl:return"Portal";case ml:return(l.displayName||"Context")+".Provider";case Vl:return(l._context.displayName||"Context")+".Consumer";case Yl:var t=l.render;return l=l.displayName,l||(l=t.displayName||t.name||"",l=l!==""?"ForwardRef("+l+")":"ForwardRef"),l;case jl:return t=l.displayName||null,t!==null?t:At(l.type)||"Memo";case Q:t=l._payload,l=l._init;try{return At(l(t))}catch{}}return null}var Al=Array.isArray,x=p.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H=y.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D={pending:!1,data:null,method:null,action:null},rl=[],o=-1;function M(l){return{current:l}}function j(l){0>o||(l.current=rl[o],rl[o]=null,o--)}function N(l,t){o++,rl[o]=l.current,l.current=t}var q=M(null),el=M(null),k=M(null),cl=M(null);function gl(l,t){switch(N(k,t),N(el,l),N(q,null),t.nodeType){case 9:case 11:l=(l=t.documentElement)&&(l=l.namespaceURI)?Jo(l):0;break;default:if(l=t.tagName,t=t.namespaceURI)t=Jo(t),l=Wo(t,l);else switch(l){case"svg":l=1;break;case"math":l=2;break;default:l=0}}j(q),N(q,l)}function at(){j(q),j(el),j(k)}function Ft(l){l.memoizedState!==null&&N(cl,l);var t=q.current,e=Wo(t,l.type);t!==e&&(N(el,l),N(q,e))}function Pt(l){el.current===l&&(j(q),j(el)),cl.current===l&&(j(cl),_u._currentValue=D)}var It=Object.prototype.hasOwnProperty,Pn=f.unstable_scheduleCallback,In=f.unstable_cancelCallback,lm=f.unstable_shouldYield,tm=f.unstable_requestPaint,Ot=f.unstable_now,em=f.unstable_getCurrentPriorityLevel,Mf=f.unstable_ImmediatePriority,Of=f.unstable_UserBlockingPriority,Cu=f.unstable_NormalPriority,am=f.unstable_LowPriority,Df=f.unstable_IdlePriority,um=f.log,nm=f.unstable_setDisableYieldValue,ja=null,ut=null;function le(l){if(typeof um=="function"&&nm(l),ut&&typeof ut.setStrictMode=="function")try{ut.setStrictMode(ja,l)}catch{}}var nt=Math.clz32?Math.clz32:fm,im=Math.log,cm=Math.LN2;function fm(l){return l>>>=0,l===0?32:31-(im(l)/cm|0)|0}var qu=256,Bu=4194304;function ze(l){var t=l&42;if(t!==0)return t;switch(l&-l){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return l&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return l&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return l}}function Yu(l,t,e){var a=l.pendingLanes;if(a===0)return 0;var u=0,n=l.suspendedLanes,i=l.pingedLanes;l=l.warmLanes;var c=a&134217727;return c!==0?(a=c&~n,a!==0?u=ze(a):(i&=c,i!==0?u=ze(i):e||(e=c&~l,e!==0&&(u=ze(e))))):(c=a&~n,c!==0?u=ze(c):i!==0?u=ze(i):e||(e=a&~l,e!==0&&(u=ze(e)))),u===0?0:t!==0&&t!==u&&(t&n)===0&&(n=u&-u,e=t&-t,n>=e||n===32&&(e&4194048)!==0)?t:u}function Ca(l,t){return(l.pendingLanes&~(l.suspendedLanes&~l.pingedLanes)&t)===0}function sm(l,t){switch(l){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Rf(){var l=qu;return qu<<=1,(qu&4194048)===0&&(qu=256),l}function Nf(){var l=Bu;return Bu<<=1,(Bu&62914560)===0&&(Bu=4194304),l}function li(l){for(var t=[],e=0;31>e;e++)t.push(l);return t}function qa(l,t){l.pendingLanes|=t,t!==268435456&&(l.suspendedLanes=0,l.pingedLanes=0,l.warmLanes=0)}function rm(l,t,e,a,u,n){var i=l.pendingLanes;l.pendingLanes=e,l.suspendedLanes=0,l.pingedLanes=0,l.warmLanes=0,l.expiredLanes&=e,l.entangledLanes&=e,l.errorRecoveryDisabledLanes&=e,l.shellSuspendCounter=0;var c=l.entanglements,r=l.expirationTimes,v=l.hiddenUpdates;for(e=i&~e;0<e;){var A=31-nt(e),z=1<<A;c[A]=0,r[A]=-1;var g=v[A];if(g!==null)for(v[A]=null,A=0;A<g.length;A++){var b=g[A];b!==null&&(b.lane&=-536870913)}e&=~z}a!==0&&Uf(l,a,0),n!==0&&u===0&&l.tag!==0&&(l.suspendedLanes|=n&~(i&~t))}function Uf(l,t,e){l.pendingLanes|=t,l.suspendedLanes&=~t;var a=31-nt(t);l.entangledLanes|=t,l.entanglements[a]=l.entanglements[a]|1073741824|e&4194090}function Hf(l,t){var e=l.entangledLanes|=t;for(l=l.entanglements;e;){var a=31-nt(e),u=1<<a;u&t|l[a]&t&&(l[a]|=t),e&=~u}}function ti(l){switch(l){case 2:l=1;break;case 8:l=4;break;case 32:l=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:l=128;break;case 268435456:l=134217728;break;default:l=0}return l}function ei(l){return l&=-l,2<l?8<l?(l&134217727)!==0?32:268435456:8:2}function jf(){var l=H.p;return l!==0?l:(l=window.event,l===void 0?32:hd(l.type))}function om(l,t){var e=H.p;try{return H.p=l,t()}finally{H.p=e}}var te=Math.random().toString(36).slice(2),Jl="__reactFiber$"+te,Pl="__reactProps$"+te,Le="__reactContainer$"+te,ai="__reactEvents$"+te,dm="__reactListeners$"+te,mm="__reactHandles$"+te,Cf="__reactResources$"+te,Ba="__reactMarker$"+te;function ui(l){delete l[Jl],delete l[Pl],delete l[ai],delete l[dm],delete l[mm]}function Ke(l){var t=l[Jl];if(t)return t;for(var e=l.parentNode;e;){if(t=e[Le]||e[Jl]){if(e=t.alternate,t.child!==null||e!==null&&e.child!==null)for(l=Io(l);l!==null;){if(e=l[Jl])return e;l=Io(l)}return t}l=e,e=l.parentNode}return null}function ke(l){if(l=l[Jl]||l[Le]){var t=l.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return l}return null}function Ya(l){var t=l.tag;if(t===5||t===26||t===27||t===6)return l.stateNode;throw Error(s(33))}function Je(l){var t=l[Cf];return t||(t=l[Cf]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Gl(l){l[Ba]=!0}var qf=new Set,Bf={};function _e(l,t){We(l,t),We(l+"Capture",t)}function We(l,t){for(Bf[l]=t,l=0;l<t.length;l++)qf.add(t[l])}var hm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Yf={},Gf={};function vm(l){return It.call(Gf,l)?!0:It.call(Yf,l)?!1:hm.test(l)?Gf[l]=!0:(Yf[l]=!0,!1)}function Gu(l,t,e){if(vm(t))if(e===null)l.removeAttribute(t);else{switch(typeof e){case"undefined":case"function":case"symbol":l.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){l.removeAttribute(t);return}}l.setAttribute(t,""+e)}}function wu(l,t,e){if(e===null)l.removeAttribute(t);else{switch(typeof e){case"undefined":case"function":case"symbol":case"boolean":l.removeAttribute(t);return}l.setAttribute(t,""+e)}}function Ct(l,t,e,a){if(a===null)l.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":l.removeAttribute(e);return}l.setAttributeNS(t,e,""+a)}}var ni,wf;function $e(l){if(ni===void 0)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);ni=t&&t[1]||"",wf=-1<e.stack.indexOf(`
    at`)?" (<anonymous>)":-1<e.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ni+l+wf}var ii=!1;function ci(l,t){if(!l||ii)return"";ii=!0;var e=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var z=function(){throw Error()};if(Object.defineProperty(z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(z,[])}catch(b){var g=b}Reflect.construct(l,[],z)}else{try{z.call()}catch(b){g=b}l.call(z.prototype)}}else{try{throw Error()}catch(b){g=b}(z=l())&&typeof z.catch=="function"&&z.catch(function(){})}}catch(b){if(b&&g&&typeof b.stack=="string")return[b.stack,g.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var n=a.DetermineComponentFrameRoot(),i=n[0],c=n[1];if(i&&c){var r=i.split(`
`),v=c.split(`
`);for(u=a=0;a<r.length&&!r[a].includes("DetermineComponentFrameRoot");)a++;for(;u<v.length&&!v[u].includes("DetermineComponentFrameRoot");)u++;if(a===r.length||u===v.length)for(a=r.length-1,u=v.length-1;1<=a&&0<=u&&r[a]!==v[u];)u--;for(;1<=a&&0<=u;a--,u--)if(r[a]!==v[u]){if(a!==1||u!==1)do if(a--,u--,0>u||r[a]!==v[u]){var A=`
`+r[a].replace(" at new "," at ");return l.displayName&&A.includes("<anonymous>")&&(A=A.replace("<anonymous>",l.displayName)),A}while(1<=a&&0<=u);break}}}finally{ii=!1,Error.prepareStackTrace=e}return(e=l?l.displayName||l.name:"")?$e(e):""}function ym(l){switch(l.tag){case 26:case 27:case 5:return $e(l.type);case 16:return $e("Lazy");case 13:return $e("Suspense");case 19:return $e("SuspenseList");case 0:case 15:return ci(l.type,!1);case 11:return ci(l.type.render,!1);case 1:return ci(l.type,!0);case 31:return $e("Activity");default:return""}}function Xf(l){try{var t="";do t+=ym(l),l=l.return;while(l);return t}catch(e){return`
Error generating stack: `+e.message+`
`+e.stack}}function ht(l){switch(typeof l){case"bigint":case"boolean":case"number":case"string":case"undefined":return l;case"object":return l;default:return""}}function Qf(l){var t=l.type;return(l=l.nodeName)&&l.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function gm(l){var t=Qf(l)?"checked":"value",e=Object.getOwnPropertyDescriptor(l.constructor.prototype,t),a=""+l[t];if(!l.hasOwnProperty(t)&&typeof e<"u"&&typeof e.get=="function"&&typeof e.set=="function"){var u=e.get,n=e.set;return Object.defineProperty(l,t,{configurable:!0,get:function(){return u.call(this)},set:function(i){a=""+i,n.call(this,i)}}),Object.defineProperty(l,t,{enumerable:e.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){l._valueTracker=null,delete l[t]}}}}function Xu(l){l._valueTracker||(l._valueTracker=gm(l))}function Zf(l){if(!l)return!1;var t=l._valueTracker;if(!t)return!0;var e=t.getValue(),a="";return l&&(a=Qf(l)?l.checked?"true":"false":l.value),l=a,l!==e?(t.setValue(l),!0):!1}function Qu(l){if(l=l||(typeof document<"u"?document:void 0),typeof l>"u")return null;try{return l.activeElement||l.body}catch{return l.body}}var bm=/[\n"\\]/g;function vt(l){return l.replace(bm,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function fi(l,t,e,a,u,n,i,c){l.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?l.type=i:l.removeAttribute("type"),t!=null?i==="number"?(t===0&&l.value===""||l.value!=t)&&(l.value=""+ht(t)):l.value!==""+ht(t)&&(l.value=""+ht(t)):i!=="submit"&&i!=="reset"||l.removeAttribute("value"),t!=null?si(l,i,ht(t)):e!=null?si(l,i,ht(e)):a!=null&&l.removeAttribute("value"),u==null&&n!=null&&(l.defaultChecked=!!n),u!=null&&(l.checked=u&&typeof u!="function"&&typeof u!="symbol"),c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"?l.name=""+ht(c):l.removeAttribute("name")}function Vf(l,t,e,a,u,n,i,c){if(n!=null&&typeof n!="function"&&typeof n!="symbol"&&typeof n!="boolean"&&(l.type=n),t!=null||e!=null){if(!(n!=="submit"&&n!=="reset"||t!=null))return;e=e!=null?""+ht(e):"",t=t!=null?""+ht(t):e,c||t===l.value||(l.value=t),l.defaultValue=t}a=a??u,a=typeof a!="function"&&typeof a!="symbol"&&!!a,l.checked=c?l.checked:!!a,l.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(l.name=i)}function si(l,t,e){t==="number"&&Qu(l.ownerDocument)===l||l.defaultValue===""+e||(l.defaultValue=""+e)}function Fe(l,t,e,a){if(l=l.options,t){t={};for(var u=0;u<e.length;u++)t["$"+e[u]]=!0;for(e=0;e<l.length;e++)u=t.hasOwnProperty("$"+l[e].value),l[e].selected!==u&&(l[e].selected=u),u&&a&&(l[e].defaultSelected=!0)}else{for(e=""+ht(e),t=null,u=0;u<l.length;u++){if(l[u].value===e){l[u].selected=!0,a&&(l[u].defaultSelected=!0);return}t!==null||l[u].disabled||(t=l[u])}t!==null&&(t.selected=!0)}}function Lf(l,t,e){if(t!=null&&(t=""+ht(t),t!==l.value&&(l.value=t),e==null)){l.defaultValue!==t&&(l.defaultValue=t);return}l.defaultValue=e!=null?""+ht(e):""}function Kf(l,t,e,a){if(t==null){if(a!=null){if(e!=null)throw Error(s(92));if(Al(a)){if(1<a.length)throw Error(s(93));a=a[0]}e=a}e==null&&(e=""),t=e}e=ht(t),l.defaultValue=e,a=l.textContent,a===e&&a!==""&&a!==null&&(l.value=a)}function Pe(l,t){if(t){var e=l.firstChild;if(e&&e===l.lastChild&&e.nodeType===3){e.nodeValue=t;return}}l.textContent=t}var pm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function kf(l,t,e){var a=t.indexOf("--")===0;e==null||typeof e=="boolean"||e===""?a?l.setProperty(t,""):t==="float"?l.cssFloat="":l[t]="":a?l.setProperty(t,e):typeof e!="number"||e===0||pm.has(t)?t==="float"?l.cssFloat=e:l[t]=(""+e).trim():l[t]=e+"px"}function Jf(l,t,e){if(t!=null&&typeof t!="object")throw Error(s(62));if(l=l.style,e!=null){for(var a in e)!e.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?l.setProperty(a,""):a==="float"?l.cssFloat="":l[a]="");for(var u in t)a=t[u],t.hasOwnProperty(u)&&e[u]!==a&&kf(l,u,a)}else for(var n in t)t.hasOwnProperty(n)&&kf(l,n,t[n])}function ri(l){if(l.indexOf("-")===-1)return!1;switch(l){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Sm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),xm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Zu(l){return xm.test(""+l)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":l}var oi=null;function di(l){return l=l.target||l.srcElement||window,l.correspondingUseElement&&(l=l.correspondingUseElement),l.nodeType===3?l.parentNode:l}var Ie=null,la=null;function Wf(l){var t=ke(l);if(t&&(l=t.stateNode)){var e=l[Pl]||null;l:switch(l=t.stateNode,t.type){case"input":if(fi(l,e.value,e.defaultValue,e.defaultValue,e.checked,e.defaultChecked,e.type,e.name),t=e.name,e.type==="radio"&&t!=null){for(e=l;e.parentNode;)e=e.parentNode;for(e=e.querySelectorAll('input[name="'+vt(""+t)+'"][type="radio"]'),t=0;t<e.length;t++){var a=e[t];if(a!==l&&a.form===l.form){var u=a[Pl]||null;if(!u)throw Error(s(90));fi(a,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<e.length;t++)a=e[t],a.form===l.form&&Zf(a)}break l;case"textarea":Lf(l,e.value,e.defaultValue);break l;case"select":t=e.value,t!=null&&Fe(l,!!e.multiple,t,!1)}}}var mi=!1;function $f(l,t,e){if(mi)return l(t,e);mi=!0;try{var a=l(t);return a}finally{if(mi=!1,(Ie!==null||la!==null)&&(Dn(),Ie&&(t=Ie,l=la,la=Ie=null,Wf(t),l)))for(t=0;t<l.length;t++)Wf(l[t])}}function Ga(l,t){var e=l.stateNode;if(e===null)return null;var a=e[Pl]||null;if(a===null)return null;e=a[t];l:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(l=l.type,a=!(l==="button"||l==="input"||l==="select"||l==="textarea")),l=!a;break l;default:l=!1}if(l)return null;if(e&&typeof e!="function")throw Error(s(231,t,typeof e));return e}var qt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),hi=!1;if(qt)try{var wa={};Object.defineProperty(wa,"passive",{get:function(){hi=!0}}),window.addEventListener("test",wa,wa),window.removeEventListener("test",wa,wa)}catch{hi=!1}var ee=null,vi=null,Vu=null;function Ff(){if(Vu)return Vu;var l,t=vi,e=t.length,a,u="value"in ee?ee.value:ee.textContent,n=u.length;for(l=0;l<e&&t[l]===u[l];l++);var i=e-l;for(a=1;a<=i&&t[e-a]===u[n-a];a++);return Vu=u.slice(l,1<a?1-a:void 0)}function Lu(l){var t=l.keyCode;return"charCode"in l?(l=l.charCode,l===0&&t===13&&(l=13)):l=t,l===10&&(l=13),32<=l||l===13?l:0}function Ku(){return!0}function Pf(){return!1}function Il(l){function t(e,a,u,n,i){this._reactName=e,this._targetInst=u,this.type=a,this.nativeEvent=n,this.target=i,this.currentTarget=null;for(var c in l)l.hasOwnProperty(c)&&(e=l[c],this[c]=e?e(n):n[c]);return this.isDefaultPrevented=(n.defaultPrevented!=null?n.defaultPrevented:n.returnValue===!1)?Ku:Pf,this.isPropagationStopped=Pf,this}return R(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():typeof e.returnValue!="unknown"&&(e.returnValue=!1),this.isDefaultPrevented=Ku)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():typeof e.cancelBubble!="unknown"&&(e.cancelBubble=!0),this.isPropagationStopped=Ku)},persist:function(){},isPersistent:Ku}),t}var Me={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(l){return l.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ku=Il(Me),Xa=R({},Me,{view:0,detail:0}),Tm=Il(Xa),yi,gi,Qa,Ju=R({},Xa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pi,button:0,buttons:0,relatedTarget:function(l){return l.relatedTarget===void 0?l.fromElement===l.srcElement?l.toElement:l.fromElement:l.relatedTarget},movementX:function(l){return"movementX"in l?l.movementX:(l!==Qa&&(Qa&&l.type==="mousemove"?(yi=l.screenX-Qa.screenX,gi=l.screenY-Qa.screenY):gi=yi=0,Qa=l),yi)},movementY:function(l){return"movementY"in l?l.movementY:gi}}),If=Il(Ju),Am=R({},Ju,{dataTransfer:0}),Em=Il(Am),zm=R({},Xa,{relatedTarget:0}),bi=Il(zm),_m=R({},Me,{animationName:0,elapsedTime:0,pseudoElement:0}),Mm=Il(_m),Om=R({},Me,{clipboardData:function(l){return"clipboardData"in l?l.clipboardData:window.clipboardData}}),Dm=Il(Om),Rm=R({},Me,{data:0}),ls=Il(Rm),Nm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Um={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Hm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function jm(l){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(l):(l=Hm[l])?!!t[l]:!1}function pi(){return jm}var Cm=R({},Xa,{key:function(l){if(l.key){var t=Nm[l.key]||l.key;if(t!=="Unidentified")return t}return l.type==="keypress"?(l=Lu(l),l===13?"Enter":String.fromCharCode(l)):l.type==="keydown"||l.type==="keyup"?Um[l.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pi,charCode:function(l){return l.type==="keypress"?Lu(l):0},keyCode:function(l){return l.type==="keydown"||l.type==="keyup"?l.keyCode:0},which:function(l){return l.type==="keypress"?Lu(l):l.type==="keydown"||l.type==="keyup"?l.keyCode:0}}),qm=Il(Cm),Bm=R({},Ju,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ts=Il(Bm),Ym=R({},Xa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pi}),Gm=Il(Ym),wm=R({},Me,{propertyName:0,elapsedTime:0,pseudoElement:0}),Xm=Il(wm),Qm=R({},Ju,{deltaX:function(l){return"deltaX"in l?l.deltaX:"wheelDeltaX"in l?-l.wheelDeltaX:0},deltaY:function(l){return"deltaY"in l?l.deltaY:"wheelDeltaY"in l?-l.wheelDeltaY:"wheelDelta"in l?-l.wheelDelta:0},deltaZ:0,deltaMode:0}),Zm=Il(Qm),Vm=R({},Me,{newState:0,oldState:0}),Lm=Il(Vm),Km=[9,13,27,32],Si=qt&&"CompositionEvent"in window,Za=null;qt&&"documentMode"in document&&(Za=document.documentMode);var km=qt&&"TextEvent"in window&&!Za,es=qt&&(!Si||Za&&8<Za&&11>=Za),as=" ",us=!1;function ns(l,t){switch(l){case"keyup":return Km.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function is(l){return l=l.detail,typeof l=="object"&&"data"in l?l.data:null}var ta=!1;function Jm(l,t){switch(l){case"compositionend":return is(t);case"keypress":return t.which!==32?null:(us=!0,as);case"textInput":return l=t.data,l===as&&us?null:l;default:return null}}function Wm(l,t){if(ta)return l==="compositionend"||!Si&&ns(l,t)?(l=Ff(),Vu=vi=ee=null,ta=!1,l):null;switch(l){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return es&&t.locale!=="ko"?null:t.data;default:return null}}var $m={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function cs(l){var t=l&&l.nodeName&&l.nodeName.toLowerCase();return t==="input"?!!$m[l.type]:t==="textarea"}function fs(l,t,e,a){Ie?la?la.push(a):la=[a]:Ie=a,t=Cn(t,"onChange"),0<t.length&&(e=new ku("onChange","change",null,e,a),l.push({event:e,listeners:t}))}var Va=null,La=null;function Fm(l){Zo(l,0)}function Wu(l){var t=Ya(l);if(Zf(t))return l}function ss(l,t){if(l==="change")return t}var rs=!1;if(qt){var xi;if(qt){var Ti="oninput"in document;if(!Ti){var os=document.createElement("div");os.setAttribute("oninput","return;"),Ti=typeof os.oninput=="function"}xi=Ti}else xi=!1;rs=xi&&(!document.documentMode||9<document.documentMode)}function ds(){Va&&(Va.detachEvent("onpropertychange",ms),La=Va=null)}function ms(l){if(l.propertyName==="value"&&Wu(La)){var t=[];fs(t,La,l,di(l)),$f(Fm,t)}}function Pm(l,t,e){l==="focusin"?(ds(),Va=t,La=e,Va.attachEvent("onpropertychange",ms)):l==="focusout"&&ds()}function Im(l){if(l==="selectionchange"||l==="keyup"||l==="keydown")return Wu(La)}function lh(l,t){if(l==="click")return Wu(t)}function th(l,t){if(l==="input"||l==="change")return Wu(t)}function eh(l,t){return l===t&&(l!==0||1/l===1/t)||l!==l&&t!==t}var it=typeof Object.is=="function"?Object.is:eh;function Ka(l,t){if(it(l,t))return!0;if(typeof l!="object"||l===null||typeof t!="object"||t===null)return!1;var e=Object.keys(l),a=Object.keys(t);if(e.length!==a.length)return!1;for(a=0;a<e.length;a++){var u=e[a];if(!It.call(t,u)||!it(l[u],t[u]))return!1}return!0}function hs(l){for(;l&&l.firstChild;)l=l.firstChild;return l}function vs(l,t){var e=hs(l);l=0;for(var a;e;){if(e.nodeType===3){if(a=l+e.textContent.length,l<=t&&a>=t)return{node:e,offset:t-l};l=a}l:{for(;e;){if(e.nextSibling){e=e.nextSibling;break l}e=e.parentNode}e=void 0}e=hs(e)}}function ys(l,t){return l&&t?l===t?!0:l&&l.nodeType===3?!1:t&&t.nodeType===3?ys(l,t.parentNode):"contains"in l?l.contains(t):l.compareDocumentPosition?!!(l.compareDocumentPosition(t)&16):!1:!1}function gs(l){l=l!=null&&l.ownerDocument!=null&&l.ownerDocument.defaultView!=null?l.ownerDocument.defaultView:window;for(var t=Qu(l.document);t instanceof l.HTMLIFrameElement;){try{var e=typeof t.contentWindow.location.href=="string"}catch{e=!1}if(e)l=t.contentWindow;else break;t=Qu(l.document)}return t}function Ai(l){var t=l&&l.nodeName&&l.nodeName.toLowerCase();return t&&(t==="input"&&(l.type==="text"||l.type==="search"||l.type==="tel"||l.type==="url"||l.type==="password")||t==="textarea"||l.contentEditable==="true")}var ah=qt&&"documentMode"in document&&11>=document.documentMode,ea=null,Ei=null,ka=null,zi=!1;function bs(l,t,e){var a=e.window===e?e.document:e.nodeType===9?e:e.ownerDocument;zi||ea==null||ea!==Qu(a)||(a=ea,"selectionStart"in a&&Ai(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),ka&&Ka(ka,a)||(ka=a,a=Cn(Ei,"onSelect"),0<a.length&&(t=new ku("onSelect","select",null,t,e),l.push({event:t,listeners:a}),t.target=ea)))}function Oe(l,t){var e={};return e[l.toLowerCase()]=t.toLowerCase(),e["Webkit"+l]="webkit"+t,e["Moz"+l]="moz"+t,e}var aa={animationend:Oe("Animation","AnimationEnd"),animationiteration:Oe("Animation","AnimationIteration"),animationstart:Oe("Animation","AnimationStart"),transitionrun:Oe("Transition","TransitionRun"),transitionstart:Oe("Transition","TransitionStart"),transitioncancel:Oe("Transition","TransitionCancel"),transitionend:Oe("Transition","TransitionEnd")},_i={},ps={};qt&&(ps=document.createElement("div").style,"AnimationEvent"in window||(delete aa.animationend.animation,delete aa.animationiteration.animation,delete aa.animationstart.animation),"TransitionEvent"in window||delete aa.transitionend.transition);function De(l){if(_i[l])return _i[l];if(!aa[l])return l;var t=aa[l],e;for(e in t)if(t.hasOwnProperty(e)&&e in ps)return _i[l]=t[e];return l}var Ss=De("animationend"),xs=De("animationiteration"),Ts=De("animationstart"),uh=De("transitionrun"),nh=De("transitionstart"),ih=De("transitioncancel"),As=De("transitionend"),Es=new Map,Mi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Mi.push("scrollEnd");function Et(l,t){Es.set(l,t),_e(t,[l])}var zs=new WeakMap;function yt(l,t){if(typeof l=="object"&&l!==null){var e=zs.get(l);return e!==void 0?e:(t={value:l,source:t,stack:Xf(t)},zs.set(l,t),t)}return{value:l,source:t,stack:Xf(t)}}var gt=[],ua=0,Oi=0;function $u(){for(var l=ua,t=Oi=ua=0;t<l;){var e=gt[t];gt[t++]=null;var a=gt[t];gt[t++]=null;var u=gt[t];gt[t++]=null;var n=gt[t];if(gt[t++]=null,a!==null&&u!==null){var i=a.pending;i===null?u.next=u:(u.next=i.next,i.next=u),a.pending=u}n!==0&&_s(e,u,n)}}function Fu(l,t,e,a){gt[ua++]=l,gt[ua++]=t,gt[ua++]=e,gt[ua++]=a,Oi|=a,l.lanes|=a,l=l.alternate,l!==null&&(l.lanes|=a)}function Di(l,t,e,a){return Fu(l,t,e,a),Pu(l)}function na(l,t){return Fu(l,null,null,t),Pu(l)}function _s(l,t,e){l.lanes|=e;var a=l.alternate;a!==null&&(a.lanes|=e);for(var u=!1,n=l.return;n!==null;)n.childLanes|=e,a=n.alternate,a!==null&&(a.childLanes|=e),n.tag===22&&(l=n.stateNode,l===null||l._visibility&1||(u=!0)),l=n,n=n.return;return l.tag===3?(n=l.stateNode,u&&t!==null&&(u=31-nt(e),l=n.hiddenUpdates,a=l[u],a===null?l[u]=[t]:a.push(t),t.lane=e|536870912),n):null}function Pu(l){if(50<bu)throw bu=0,Cc=null,Error(s(185));for(var t=l.return;t!==null;)l=t,t=l.return;return l.tag===3?l.stateNode:null}var ia={};function ch(l,t,e,a){this.tag=l,this.key=e,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ct(l,t,e,a){return new ch(l,t,e,a)}function Ri(l){return l=l.prototype,!(!l||!l.isReactComponent)}function Bt(l,t){var e=l.alternate;return e===null?(e=ct(l.tag,t,l.key,l.mode),e.elementType=l.elementType,e.type=l.type,e.stateNode=l.stateNode,e.alternate=l,l.alternate=e):(e.pendingProps=t,e.type=l.type,e.flags=0,e.subtreeFlags=0,e.deletions=null),e.flags=l.flags&65011712,e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},e.sibling=l.sibling,e.index=l.index,e.ref=l.ref,e.refCleanup=l.refCleanup,e}function Ms(l,t){l.flags&=65011714;var e=l.alternate;return e===null?(l.childLanes=0,l.lanes=t,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,l.type=e.type,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),l}function Iu(l,t,e,a,u,n){var i=0;if(a=l,typeof l=="function")Ri(l)&&(i=1);else if(typeof l=="string")i=s0(l,e,q.current)?26:l==="html"||l==="head"||l==="body"?27:5;else l:switch(l){case Nl:return l=ct(31,e,t,u),l.elementType=Nl,l.lanes=n,l;case Z:return Re(e.children,u,n,t);case W:i=8,u|=24;break;case nl:return l=ct(12,e,t,u|2),l.elementType=nl,l.lanes=n,l;case P:return l=ct(13,e,t,u),l.elementType=P,l.lanes=n,l;case Ml:return l=ct(19,e,t,u),l.elementType=Ml,l.lanes=n,l;default:if(typeof l=="object"&&l!==null)switch(l.$$typeof){case Zl:case ml:i=10;break l;case Vl:i=9;break l;case Yl:i=11;break l;case jl:i=14;break l;case Q:i=16,a=null;break l}i=29,e=Error(s(130,l===null?"null":typeof l,"")),a=null}return t=ct(i,e,t,u),t.elementType=l,t.type=a,t.lanes=n,t}function Re(l,t,e,a){return l=ct(7,l,a,t),l.lanes=e,l}function Ni(l,t,e){return l=ct(6,l,null,t),l.lanes=e,l}function Ui(l,t,e){return t=ct(4,l.children!==null?l.children:[],l.key,t),t.lanes=e,t.stateNode={containerInfo:l.containerInfo,pendingChildren:null,implementation:l.implementation},t}var ca=[],fa=0,ln=null,tn=0,bt=[],pt=0,Ne=null,Yt=1,Gt="";function Ue(l,t){ca[fa++]=tn,ca[fa++]=ln,ln=l,tn=t}function Os(l,t,e){bt[pt++]=Yt,bt[pt++]=Gt,bt[pt++]=Ne,Ne=l;var a=Yt;l=Gt;var u=32-nt(a)-1;a&=~(1<<u),e+=1;var n=32-nt(t)+u;if(30<n){var i=u-u%5;n=(a&(1<<i)-1).toString(32),a>>=i,u-=i,Yt=1<<32-nt(t)+u|e<<u|a,Gt=n+l}else Yt=1<<n|e<<u|a,Gt=l}function Hi(l){l.return!==null&&(Ue(l,1),Os(l,1,0))}function ji(l){for(;l===ln;)ln=ca[--fa],ca[fa]=null,tn=ca[--fa],ca[fa]=null;for(;l===Ne;)Ne=bt[--pt],bt[pt]=null,Gt=bt[--pt],bt[pt]=null,Yt=bt[--pt],bt[pt]=null}var Fl=null,zl=null,sl=!1,He=null,Dt=!1,Ci=Error(s(519));function je(l){var t=Error(s(418,""));throw $a(yt(t,l)),Ci}function Ds(l){var t=l.stateNode,e=l.type,a=l.memoizedProps;switch(t[Jl]=l,t[Pl]=a,e){case"dialog":ul("cancel",t),ul("close",t);break;case"iframe":case"object":case"embed":ul("load",t);break;case"video":case"audio":for(e=0;e<Su.length;e++)ul(Su[e],t);break;case"source":ul("error",t);break;case"img":case"image":case"link":ul("error",t),ul("load",t);break;case"details":ul("toggle",t);break;case"input":ul("invalid",t),Vf(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Xu(t);break;case"select":ul("invalid",t);break;case"textarea":ul("invalid",t),Kf(t,a.value,a.defaultValue,a.children),Xu(t)}e=a.children,typeof e!="string"&&typeof e!="number"&&typeof e!="bigint"||t.textContent===""+e||a.suppressHydrationWarning===!0||ko(t.textContent,e)?(a.popover!=null&&(ul("beforetoggle",t),ul("toggle",t)),a.onScroll!=null&&ul("scroll",t),a.onScrollEnd!=null&&ul("scrollend",t),a.onClick!=null&&(t.onclick=qn),t=!0):t=!1,t||je(l)}function Rs(l){for(Fl=l.return;Fl;)switch(Fl.tag){case 5:case 13:Dt=!1;return;case 27:case 3:Dt=!0;return;default:Fl=Fl.return}}function Ja(l){if(l!==Fl)return!1;if(!sl)return Rs(l),sl=!0,!1;var t=l.tag,e;if((e=t!==3&&t!==27)&&((e=t===5)&&(e=l.type,e=!(e!=="form"&&e!=="button")||Fc(l.type,l.memoizedProps)),e=!e),e&&zl&&je(l),Rs(l),t===13){if(l=l.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(s(317));l:{for(l=l.nextSibling,t=0;l;){if(l.nodeType===8)if(e=l.data,e==="/$"){if(t===0){zl=_t(l.nextSibling);break l}t--}else e!=="$"&&e!=="$!"&&e!=="$?"||t++;l=l.nextSibling}zl=null}}else t===27?(t=zl,be(l.type)?(l=tf,tf=null,zl=l):zl=t):zl=Fl?_t(l.stateNode.nextSibling):null;return!0}function Wa(){zl=Fl=null,sl=!1}function Ns(){var l=He;return l!==null&&(et===null?et=l:et.push.apply(et,l),He=null),l}function $a(l){He===null?He=[l]:He.push(l)}var qi=M(null),Ce=null,wt=null;function ae(l,t,e){N(qi,t._currentValue),t._currentValue=e}function Xt(l){l._currentValue=qi.current,j(qi)}function Bi(l,t,e){for(;l!==null;){var a=l.alternate;if((l.childLanes&t)!==t?(l.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),l===e)break;l=l.return}}function Yi(l,t,e,a){var u=l.child;for(u!==null&&(u.return=l);u!==null;){var n=u.dependencies;if(n!==null){var i=u.child;n=n.firstContext;l:for(;n!==null;){var c=n;n=u;for(var r=0;r<t.length;r++)if(c.context===t[r]){n.lanes|=e,c=n.alternate,c!==null&&(c.lanes|=e),Bi(n.return,e,l),a||(i=null);break l}n=c.next}}else if(u.tag===18){if(i=u.return,i===null)throw Error(s(341));i.lanes|=e,n=i.alternate,n!==null&&(n.lanes|=e),Bi(i,e,l),i=null}else i=u.child;if(i!==null)i.return=u;else for(i=u;i!==null;){if(i===l){i=null;break}if(u=i.sibling,u!==null){u.return=i.return,i=u;break}i=i.return}u=i}}function Fa(l,t,e,a){l=null;for(var u=t,n=!1;u!==null;){if(!n){if((u.flags&524288)!==0)n=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var i=u.alternate;if(i===null)throw Error(s(387));if(i=i.memoizedProps,i!==null){var c=u.type;it(u.pendingProps.value,i.value)||(l!==null?l.push(c):l=[c])}}else if(u===cl.current){if(i=u.alternate,i===null)throw Error(s(387));i.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(l!==null?l.push(_u):l=[_u])}u=u.return}l!==null&&Yi(t,l,e,a),t.flags|=262144}function en(l){for(l=l.firstContext;l!==null;){if(!it(l.context._currentValue,l.memoizedValue))return!0;l=l.next}return!1}function qe(l){Ce=l,wt=null,l=l.dependencies,l!==null&&(l.firstContext=null)}function Wl(l){return Us(Ce,l)}function an(l,t){return Ce===null&&qe(l),Us(l,t)}function Us(l,t){var e=t._currentValue;if(t={context:t,memoizedValue:e,next:null},wt===null){if(l===null)throw Error(s(308));wt=t,l.dependencies={lanes:0,firstContext:t},l.flags|=524288}else wt=wt.next=t;return e}var fh=typeof AbortController<"u"?AbortController:function(){var l=[],t=this.signal={aborted:!1,addEventListener:function(e,a){l.push(a)}};this.abort=function(){t.aborted=!0,l.forEach(function(e){return e()})}},sh=f.unstable_scheduleCallback,rh=f.unstable_NormalPriority,Cl={$$typeof:ml,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Gi(){return{controller:new fh,data:new Map,refCount:0}}function Pa(l){l.refCount--,l.refCount===0&&sh(rh,function(){l.controller.abort()})}var Ia=null,wi=0,sa=0,ra=null;function oh(l,t){if(Ia===null){var e=Ia=[];wi=0,sa=Qc(),ra={status:"pending",value:void 0,then:function(a){e.push(a)}}}return wi++,t.then(Hs,Hs),t}function Hs(){if(--wi===0&&Ia!==null){ra!==null&&(ra.status="fulfilled");var l=Ia;Ia=null,sa=0,ra=null;for(var t=0;t<l.length;t++)(0,l[t])()}}function dh(l,t){var e=[],a={status:"pending",value:null,reason:null,then:function(u){e.push(u)}};return l.then(function(){a.status="fulfilled",a.value=t;for(var u=0;u<e.length;u++)(0,e[u])(t)},function(u){for(a.status="rejected",a.reason=u,u=0;u<e.length;u++)(0,e[u])(void 0)}),a}var js=x.S;x.S=function(l,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&oh(l,t),js!==null&&js(l,t)};var Be=M(null);function Xi(){var l=Be.current;return l!==null?l:Sl.pooledCache}function un(l,t){t===null?N(Be,Be.current):N(Be,t.pool)}function Cs(){var l=Xi();return l===null?null:{parent:Cl._currentValue,pool:l}}var lu=Error(s(460)),qs=Error(s(474)),nn=Error(s(542)),Qi={then:function(){}};function Bs(l){return l=l.status,l==="fulfilled"||l==="rejected"}function cn(){}function Ys(l,t,e){switch(e=l[e],e===void 0?l.push(t):e!==t&&(t.then(cn,cn),t=e),t.status){case"fulfilled":return t.value;case"rejected":throw l=t.reason,ws(l),l;default:if(typeof t.status=="string")t.then(cn,cn);else{if(l=Sl,l!==null&&100<l.shellSuspendCounter)throw Error(s(482));l=t,l.status="pending",l.then(function(a){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=a}},function(a){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw l=t.reason,ws(l),l}throw tu=t,lu}}var tu=null;function Gs(){if(tu===null)throw Error(s(459));var l=tu;return tu=null,l}function ws(l){if(l===lu||l===nn)throw Error(s(483))}var ue=!1;function Zi(l){l.updateQueue={baseState:l.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Vi(l,t){l=l.updateQueue,t.updateQueue===l&&(t.updateQueue={baseState:l.baseState,firstBaseUpdate:l.firstBaseUpdate,lastBaseUpdate:l.lastBaseUpdate,shared:l.shared,callbacks:null})}function ne(l){return{lane:l,tag:0,payload:null,callback:null,next:null}}function ie(l,t,e){var a=l.updateQueue;if(a===null)return null;if(a=a.shared,(ol&2)!==0){var u=a.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),a.pending=t,t=Pu(l),_s(l,null,e),t}return Fu(l,a,t,e),Pu(l)}function eu(l,t,e){if(t=t.updateQueue,t!==null&&(t=t.shared,(e&4194048)!==0)){var a=t.lanes;a&=l.pendingLanes,e|=a,t.lanes=e,Hf(l,e)}}function Li(l,t){var e=l.updateQueue,a=l.alternate;if(a!==null&&(a=a.updateQueue,e===a)){var u=null,n=null;if(e=e.firstBaseUpdate,e!==null){do{var i={lane:e.lane,tag:e.tag,payload:e.payload,callback:null,next:null};n===null?u=n=i:n=n.next=i,e=e.next}while(e!==null);n===null?u=n=t:n=n.next=t}else u=n=t;e={baseState:a.baseState,firstBaseUpdate:u,lastBaseUpdate:n,shared:a.shared,callbacks:a.callbacks},l.updateQueue=e;return}l=e.lastBaseUpdate,l===null?e.firstBaseUpdate=t:l.next=t,e.lastBaseUpdate=t}var Ki=!1;function au(){if(Ki){var l=ra;if(l!==null)throw l}}function uu(l,t,e,a){Ki=!1;var u=l.updateQueue;ue=!1;var n=u.firstBaseUpdate,i=u.lastBaseUpdate,c=u.shared.pending;if(c!==null){u.shared.pending=null;var r=c,v=r.next;r.next=null,i===null?n=v:i.next=v,i=r;var A=l.alternate;A!==null&&(A=A.updateQueue,c=A.lastBaseUpdate,c!==i&&(c===null?A.firstBaseUpdate=v:c.next=v,A.lastBaseUpdate=r))}if(n!==null){var z=u.baseState;i=0,A=v=r=null,c=n;do{var g=c.lane&-536870913,b=g!==c.lane;if(b?(il&g)===g:(a&g)===g){g!==0&&g===sa&&(Ki=!0),A!==null&&(A=A.next={lane:0,tag:c.tag,payload:c.payload,callback:null,next:null});l:{var K=l,V=c;g=t;var yl=e;switch(V.tag){case 1:if(K=V.payload,typeof K=="function"){z=K.call(yl,z,g);break l}z=K;break l;case 3:K.flags=K.flags&-65537|128;case 0:if(K=V.payload,g=typeof K=="function"?K.call(yl,z,g):K,g==null)break l;z=R({},z,g);break l;case 2:ue=!0}}g=c.callback,g!==null&&(l.flags|=64,b&&(l.flags|=8192),b=u.callbacks,b===null?u.callbacks=[g]:b.push(g))}else b={lane:g,tag:c.tag,payload:c.payload,callback:c.callback,next:null},A===null?(v=A=b,r=z):A=A.next=b,i|=g;if(c=c.next,c===null){if(c=u.shared.pending,c===null)break;b=c,c=b.next,b.next=null,u.lastBaseUpdate=b,u.shared.pending=null}}while(!0);A===null&&(r=z),u.baseState=r,u.firstBaseUpdate=v,u.lastBaseUpdate=A,n===null&&(u.shared.lanes=0),he|=i,l.lanes=i,l.memoizedState=z}}function Xs(l,t){if(typeof l!="function")throw Error(s(191,l));l.call(t)}function Qs(l,t){var e=l.callbacks;if(e!==null)for(l.callbacks=null,l=0;l<e.length;l++)Xs(e[l],t)}var oa=M(null),fn=M(0);function Zs(l,t){l=Jt,N(fn,l),N(oa,t),Jt=l|t.baseLanes}function ki(){N(fn,Jt),N(oa,oa.current)}function Ji(){Jt=fn.current,j(oa),j(fn)}var ce=0,I=null,hl=null,Ul=null,sn=!1,da=!1,Ye=!1,rn=0,nu=0,ma=null,mh=0;function Dl(){throw Error(s(321))}function Wi(l,t){if(t===null)return!1;for(var e=0;e<t.length&&e<l.length;e++)if(!it(l[e],t[e]))return!1;return!0}function $i(l,t,e,a,u,n){return ce=n,I=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,x.H=l===null||l.memoizedState===null?_r:Mr,Ye=!1,n=e(a,u),Ye=!1,da&&(n=Ls(t,e,a,u)),Vs(l),n}function Vs(l){x.H=yn;var t=hl!==null&&hl.next!==null;if(ce=0,Ul=hl=I=null,sn=!1,nu=0,ma=null,t)throw Error(s(300));l===null||wl||(l=l.dependencies,l!==null&&en(l)&&(wl=!0))}function Ls(l,t,e,a){I=l;var u=0;do{if(da&&(ma=null),nu=0,da=!1,25<=u)throw Error(s(301));if(u+=1,Ul=hl=null,l.updateQueue!=null){var n=l.updateQueue;n.lastEffect=null,n.events=null,n.stores=null,n.memoCache!=null&&(n.memoCache.index=0)}x.H=Sh,n=t(e,a)}while(da);return n}function hh(){var l=x.H,t=l.useState()[0];return t=typeof t.then=="function"?iu(t):t,l=l.useState()[0],(hl!==null?hl.memoizedState:null)!==l&&(I.flags|=1024),t}function Fi(){var l=rn!==0;return rn=0,l}function Pi(l,t,e){t.updateQueue=l.updateQueue,t.flags&=-2053,l.lanes&=~e}function Ii(l){if(sn){for(l=l.memoizedState;l!==null;){var t=l.queue;t!==null&&(t.pending=null),l=l.next}sn=!1}ce=0,Ul=hl=I=null,da=!1,nu=rn=0,ma=null}function lt(){var l={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ul===null?I.memoizedState=Ul=l:Ul=Ul.next=l,Ul}function Hl(){if(hl===null){var l=I.alternate;l=l!==null?l.memoizedState:null}else l=hl.next;var t=Ul===null?I.memoizedState:Ul.next;if(t!==null)Ul=t,hl=l;else{if(l===null)throw I.alternate===null?Error(s(467)):Error(s(310));hl=l,l={memoizedState:hl.memoizedState,baseState:hl.baseState,baseQueue:hl.baseQueue,queue:hl.queue,next:null},Ul===null?I.memoizedState=Ul=l:Ul=Ul.next=l}return Ul}function lc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function iu(l){var t=nu;return nu+=1,ma===null&&(ma=[]),l=Ys(ma,l,t),t=I,(Ul===null?t.memoizedState:Ul.next)===null&&(t=t.alternate,x.H=t===null||t.memoizedState===null?_r:Mr),l}function on(l){if(l!==null&&typeof l=="object"){if(typeof l.then=="function")return iu(l);if(l.$$typeof===ml)return Wl(l)}throw Error(s(438,String(l)))}function tc(l){var t=null,e=I.updateQueue;if(e!==null&&(t=e.memoCache),t==null){var a=I.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),e===null&&(e=lc(),I.updateQueue=e),e.memoCache=t,e=t.data[t.index],e===void 0)for(e=t.data[t.index]=Array(l),a=0;a<l;a++)e[a]=jt;return t.index++,e}function Qt(l,t){return typeof t=="function"?t(l):t}function dn(l){var t=Hl();return ec(t,hl,l)}function ec(l,t,e){var a=l.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=e;var u=l.baseQueue,n=a.pending;if(n!==null){if(u!==null){var i=u.next;u.next=n.next,n.next=i}t.baseQueue=u=n,a.pending=null}if(n=l.baseState,u===null)l.memoizedState=n;else{t=u.next;var c=i=null,r=null,v=t,A=!1;do{var z=v.lane&-536870913;if(z!==v.lane?(il&z)===z:(ce&z)===z){var g=v.revertLane;if(g===0)r!==null&&(r=r.next={lane:0,revertLane:0,action:v.action,hasEagerState:v.hasEagerState,eagerState:v.eagerState,next:null}),z===sa&&(A=!0);else if((ce&g)===g){v=v.next,g===sa&&(A=!0);continue}else z={lane:0,revertLane:v.revertLane,action:v.action,hasEagerState:v.hasEagerState,eagerState:v.eagerState,next:null},r===null?(c=r=z,i=n):r=r.next=z,I.lanes|=g,he|=g;z=v.action,Ye&&e(n,z),n=v.hasEagerState?v.eagerState:e(n,z)}else g={lane:z,revertLane:v.revertLane,action:v.action,hasEagerState:v.hasEagerState,eagerState:v.eagerState,next:null},r===null?(c=r=g,i=n):r=r.next=g,I.lanes|=z,he|=z;v=v.next}while(v!==null&&v!==t);if(r===null?i=n:r.next=c,!it(n,l.memoizedState)&&(wl=!0,A&&(e=ra,e!==null)))throw e;l.memoizedState=n,l.baseState=i,l.baseQueue=r,a.lastRenderedState=n}return u===null&&(a.lanes=0),[l.memoizedState,a.dispatch]}function ac(l){var t=Hl(),e=t.queue;if(e===null)throw Error(s(311));e.lastRenderedReducer=l;var a=e.dispatch,u=e.pending,n=t.memoizedState;if(u!==null){e.pending=null;var i=u=u.next;do n=l(n,i.action),i=i.next;while(i!==u);it(n,t.memoizedState)||(wl=!0),t.memoizedState=n,t.baseQueue===null&&(t.baseState=n),e.lastRenderedState=n}return[n,a]}function Ks(l,t,e){var a=I,u=Hl(),n=sl;if(n){if(e===void 0)throw Error(s(407));e=e()}else e=t();var i=!it((hl||u).memoizedState,e);i&&(u.memoizedState=e,wl=!0),u=u.queue;var c=Ws.bind(null,a,u,l);if(cu(2048,8,c,[l]),u.getSnapshot!==t||i||Ul!==null&&Ul.memoizedState.tag&1){if(a.flags|=2048,ha(9,mn(),Js.bind(null,a,u,e,t),null),Sl===null)throw Error(s(349));n||(ce&124)!==0||ks(a,t,e)}return e}function ks(l,t,e){l.flags|=16384,l={getSnapshot:t,value:e},t=I.updateQueue,t===null?(t=lc(),I.updateQueue=t,t.stores=[l]):(e=t.stores,e===null?t.stores=[l]:e.push(l))}function Js(l,t,e,a){t.value=e,t.getSnapshot=a,$s(t)&&Fs(l)}function Ws(l,t,e){return e(function(){$s(t)&&Fs(l)})}function $s(l){var t=l.getSnapshot;l=l.value;try{var e=t();return!it(l,e)}catch{return!0}}function Fs(l){var t=na(l,2);t!==null&&dt(t,l,2)}function uc(l){var t=lt();if(typeof l=="function"){var e=l;if(l=e(),Ye){le(!0);try{e()}finally{le(!1)}}}return t.memoizedState=t.baseState=l,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qt,lastRenderedState:l},t}function Ps(l,t,e,a){return l.baseState=e,ec(l,hl,typeof a=="function"?a:Qt)}function vh(l,t,e,a,u){if(vn(l))throw Error(s(485));if(l=t.action,l!==null){var n={payload:u,action:l,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){n.listeners.push(i)}};x.T!==null?e(!0):n.isTransition=!1,a(n),e=t.pending,e===null?(n.next=t.pending=n,Is(t,n)):(n.next=e.next,t.pending=e.next=n)}}function Is(l,t){var e=t.action,a=t.payload,u=l.state;if(t.isTransition){var n=x.T,i={};x.T=i;try{var c=e(u,a),r=x.S;r!==null&&r(i,c),lr(l,t,c)}catch(v){nc(l,t,v)}finally{x.T=n}}else try{n=e(u,a),lr(l,t,n)}catch(v){nc(l,t,v)}}function lr(l,t,e){e!==null&&typeof e=="object"&&typeof e.then=="function"?e.then(function(a){tr(l,t,a)},function(a){return nc(l,t,a)}):tr(l,t,e)}function tr(l,t,e){t.status="fulfilled",t.value=e,er(t),l.state=e,t=l.pending,t!==null&&(e=t.next,e===t?l.pending=null:(e=e.next,t.next=e,Is(l,e)))}function nc(l,t,e){var a=l.pending;if(l.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=e,er(t),t=t.next;while(t!==a)}l.action=null}function er(l){l=l.listeners;for(var t=0;t<l.length;t++)(0,l[t])()}function ar(l,t){return t}function ur(l,t){if(sl){var e=Sl.formState;if(e!==null){l:{var a=I;if(sl){if(zl){t:{for(var u=zl,n=Dt;u.nodeType!==8;){if(!n){u=null;break t}if(u=_t(u.nextSibling),u===null){u=null;break t}}n=u.data,u=n==="F!"||n==="F"?u:null}if(u){zl=_t(u.nextSibling),a=u.data==="F!";break l}}je(a)}a=!1}a&&(t=e[0])}}return e=lt(),e.memoizedState=e.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ar,lastRenderedState:t},e.queue=a,e=Ar.bind(null,I,a),a.dispatch=e,a=uc(!1),n=rc.bind(null,I,!1,a.queue),a=lt(),u={state:t,dispatch:null,action:l,pending:null},a.queue=u,e=vh.bind(null,I,u,n,e),u.dispatch=e,a.memoizedState=l,[t,e,!1]}function nr(l){var t=Hl();return ir(t,hl,l)}function ir(l,t,e){if(t=ec(l,t,ar)[0],l=dn(Qt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=iu(t)}catch(i){throw i===lu?nn:i}else a=t;t=Hl();var u=t.queue,n=u.dispatch;return e!==t.memoizedState&&(I.flags|=2048,ha(9,mn(),yh.bind(null,u,e),null)),[a,n,l]}function yh(l,t){l.action=t}function cr(l){var t=Hl(),e=hl;if(e!==null)return ir(t,e,l);Hl(),t=t.memoizedState,e=Hl();var a=e.queue.dispatch;return e.memoizedState=l,[t,a,!1]}function ha(l,t,e,a){return l={tag:l,create:e,deps:a,inst:t,next:null},t=I.updateQueue,t===null&&(t=lc(),I.updateQueue=t),e=t.lastEffect,e===null?t.lastEffect=l.next=l:(a=e.next,e.next=l,l.next=a,t.lastEffect=l),l}function mn(){return{destroy:void 0,resource:void 0}}function fr(){return Hl().memoizedState}function hn(l,t,e,a){var u=lt();a=a===void 0?null:a,I.flags|=l,u.memoizedState=ha(1|t,mn(),e,a)}function cu(l,t,e,a){var u=Hl();a=a===void 0?null:a;var n=u.memoizedState.inst;hl!==null&&a!==null&&Wi(a,hl.memoizedState.deps)?u.memoizedState=ha(t,n,e,a):(I.flags|=l,u.memoizedState=ha(1|t,n,e,a))}function sr(l,t){hn(8390656,8,l,t)}function rr(l,t){cu(2048,8,l,t)}function or(l,t){return cu(4,2,l,t)}function dr(l,t){return cu(4,4,l,t)}function mr(l,t){if(typeof t=="function"){l=l();var e=t(l);return function(){typeof e=="function"?e():t(null)}}if(t!=null)return l=l(),t.current=l,function(){t.current=null}}function hr(l,t,e){e=e!=null?e.concat([l]):null,cu(4,4,mr.bind(null,t,l),e)}function ic(){}function vr(l,t){var e=Hl();t=t===void 0?null:t;var a=e.memoizedState;return t!==null&&Wi(t,a[1])?a[0]:(e.memoizedState=[l,t],l)}function yr(l,t){var e=Hl();t=t===void 0?null:t;var a=e.memoizedState;if(t!==null&&Wi(t,a[1]))return a[0];if(a=l(),Ye){le(!0);try{l()}finally{le(!1)}}return e.memoizedState=[a,t],a}function cc(l,t,e){return e===void 0||(ce&1073741824)!==0?l.memoizedState=t:(l.memoizedState=e,l=So(),I.lanes|=l,he|=l,e)}function gr(l,t,e,a){return it(e,t)?e:oa.current!==null?(l=cc(l,e,a),it(l,t)||(wl=!0),l):(ce&42)===0?(wl=!0,l.memoizedState=e):(l=So(),I.lanes|=l,he|=l,t)}function br(l,t,e,a,u){var n=H.p;H.p=n!==0&&8>n?n:8;var i=x.T,c={};x.T=c,rc(l,!1,t,e);try{var r=u(),v=x.S;if(v!==null&&v(c,r),r!==null&&typeof r=="object"&&typeof r.then=="function"){var A=dh(r,a);fu(l,t,A,ot(l))}else fu(l,t,a,ot(l))}catch(z){fu(l,t,{then:function(){},status:"rejected",reason:z},ot())}finally{H.p=n,x.T=i}}function gh(){}function fc(l,t,e,a){if(l.tag!==5)throw Error(s(476));var u=pr(l).queue;br(l,u,t,D,e===null?gh:function(){return Sr(l),e(a)})}function pr(l){var t=l.memoizedState;if(t!==null)return t;t={memoizedState:D,baseState:D,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qt,lastRenderedState:D},next:null};var e={};return t.next={memoizedState:e,baseState:e,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qt,lastRenderedState:e},next:null},l.memoizedState=t,l=l.alternate,l!==null&&(l.memoizedState=t),t}function Sr(l){var t=pr(l).next.queue;fu(l,t,{},ot())}function sc(){return Wl(_u)}function xr(){return Hl().memoizedState}function Tr(){return Hl().memoizedState}function bh(l){for(var t=l.return;t!==null;){switch(t.tag){case 24:case 3:var e=ot();l=ne(e);var a=ie(t,l,e);a!==null&&(dt(a,t,e),eu(a,t,e)),t={cache:Gi()},l.payload=t;return}t=t.return}}function ph(l,t,e){var a=ot();e={lane:a,revertLane:0,action:e,hasEagerState:!1,eagerState:null,next:null},vn(l)?Er(t,e):(e=Di(l,t,e,a),e!==null&&(dt(e,l,a),zr(e,t,a)))}function Ar(l,t,e){var a=ot();fu(l,t,e,a)}function fu(l,t,e,a){var u={lane:a,revertLane:0,action:e,hasEagerState:!1,eagerState:null,next:null};if(vn(l))Er(t,u);else{var n=l.alternate;if(l.lanes===0&&(n===null||n.lanes===0)&&(n=t.lastRenderedReducer,n!==null))try{var i=t.lastRenderedState,c=n(i,e);if(u.hasEagerState=!0,u.eagerState=c,it(c,i))return Fu(l,t,u,0),Sl===null&&$u(),!1}catch{}finally{}if(e=Di(l,t,u,a),e!==null)return dt(e,l,a),zr(e,t,a),!0}return!1}function rc(l,t,e,a){if(a={lane:2,revertLane:Qc(),action:a,hasEagerState:!1,eagerState:null,next:null},vn(l)){if(t)throw Error(s(479))}else t=Di(l,e,a,2),t!==null&&dt(t,l,2)}function vn(l){var t=l.alternate;return l===I||t!==null&&t===I}function Er(l,t){da=sn=!0;var e=l.pending;e===null?t.next=t:(t.next=e.next,e.next=t),l.pending=t}function zr(l,t,e){if((e&4194048)!==0){var a=t.lanes;a&=l.pendingLanes,e|=a,t.lanes=e,Hf(l,e)}}var yn={readContext:Wl,use:on,useCallback:Dl,useContext:Dl,useEffect:Dl,useImperativeHandle:Dl,useLayoutEffect:Dl,useInsertionEffect:Dl,useMemo:Dl,useReducer:Dl,useRef:Dl,useState:Dl,useDebugValue:Dl,useDeferredValue:Dl,useTransition:Dl,useSyncExternalStore:Dl,useId:Dl,useHostTransitionStatus:Dl,useFormState:Dl,useActionState:Dl,useOptimistic:Dl,useMemoCache:Dl,useCacheRefresh:Dl},_r={readContext:Wl,use:on,useCallback:function(l,t){return lt().memoizedState=[l,t===void 0?null:t],l},useContext:Wl,useEffect:sr,useImperativeHandle:function(l,t,e){e=e!=null?e.concat([l]):null,hn(4194308,4,mr.bind(null,t,l),e)},useLayoutEffect:function(l,t){return hn(4194308,4,l,t)},useInsertionEffect:function(l,t){hn(4,2,l,t)},useMemo:function(l,t){var e=lt();t=t===void 0?null:t;var a=l();if(Ye){le(!0);try{l()}finally{le(!1)}}return e.memoizedState=[a,t],a},useReducer:function(l,t,e){var a=lt();if(e!==void 0){var u=e(t);if(Ye){le(!0);try{e(t)}finally{le(!1)}}}else u=t;return a.memoizedState=a.baseState=u,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:l,lastRenderedState:u},a.queue=l,l=l.dispatch=ph.bind(null,I,l),[a.memoizedState,l]},useRef:function(l){var t=lt();return l={current:l},t.memoizedState=l},useState:function(l){l=uc(l);var t=l.queue,e=Ar.bind(null,I,t);return t.dispatch=e,[l.memoizedState,e]},useDebugValue:ic,useDeferredValue:function(l,t){var e=lt();return cc(e,l,t)},useTransition:function(){var l=uc(!1);return l=br.bind(null,I,l.queue,!0,!1),lt().memoizedState=l,[!1,l]},useSyncExternalStore:function(l,t,e){var a=I,u=lt();if(sl){if(e===void 0)throw Error(s(407));e=e()}else{if(e=t(),Sl===null)throw Error(s(349));(il&124)!==0||ks(a,t,e)}u.memoizedState=e;var n={value:e,getSnapshot:t};return u.queue=n,sr(Ws.bind(null,a,n,l),[l]),a.flags|=2048,ha(9,mn(),Js.bind(null,a,n,e,t),null),e},useId:function(){var l=lt(),t=Sl.identifierPrefix;if(sl){var e=Gt,a=Yt;e=(a&~(1<<32-nt(a)-1)).toString(32)+e,t="«"+t+"R"+e,e=rn++,0<e&&(t+="H"+e.toString(32)),t+="»"}else e=mh++,t="«"+t+"r"+e.toString(32)+"»";return l.memoizedState=t},useHostTransitionStatus:sc,useFormState:ur,useActionState:ur,useOptimistic:function(l){var t=lt();t.memoizedState=t.baseState=l;var e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=e,t=rc.bind(null,I,!0,e),e.dispatch=t,[l,t]},useMemoCache:tc,useCacheRefresh:function(){return lt().memoizedState=bh.bind(null,I)}},Mr={readContext:Wl,use:on,useCallback:vr,useContext:Wl,useEffect:rr,useImperativeHandle:hr,useInsertionEffect:or,useLayoutEffect:dr,useMemo:yr,useReducer:dn,useRef:fr,useState:function(){return dn(Qt)},useDebugValue:ic,useDeferredValue:function(l,t){var e=Hl();return gr(e,hl.memoizedState,l,t)},useTransition:function(){var l=dn(Qt)[0],t=Hl().memoizedState;return[typeof l=="boolean"?l:iu(l),t]},useSyncExternalStore:Ks,useId:xr,useHostTransitionStatus:sc,useFormState:nr,useActionState:nr,useOptimistic:function(l,t){var e=Hl();return Ps(e,hl,l,t)},useMemoCache:tc,useCacheRefresh:Tr},Sh={readContext:Wl,use:on,useCallback:vr,useContext:Wl,useEffect:rr,useImperativeHandle:hr,useInsertionEffect:or,useLayoutEffect:dr,useMemo:yr,useReducer:ac,useRef:fr,useState:function(){return ac(Qt)},useDebugValue:ic,useDeferredValue:function(l,t){var e=Hl();return hl===null?cc(e,l,t):gr(e,hl.memoizedState,l,t)},useTransition:function(){var l=ac(Qt)[0],t=Hl().memoizedState;return[typeof l=="boolean"?l:iu(l),t]},useSyncExternalStore:Ks,useId:xr,useHostTransitionStatus:sc,useFormState:cr,useActionState:cr,useOptimistic:function(l,t){var e=Hl();return hl!==null?Ps(e,hl,l,t):(e.baseState=l,[l,e.queue.dispatch])},useMemoCache:tc,useCacheRefresh:Tr},va=null,su=0;function gn(l){var t=su;return su+=1,va===null&&(va=[]),Ys(va,l,t)}function ru(l,t){t=t.props.ref,l.ref=t!==void 0?t:null}function bn(l,t){throw t.$$typeof===J?Error(s(525)):(l=Object.prototype.toString.call(t),Error(s(31,l==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":l)))}function Or(l){var t=l._init;return t(l._payload)}function Dr(l){function t(m,d){if(l){var h=m.deletions;h===null?(m.deletions=[d],m.flags|=16):h.push(d)}}function e(m,d){if(!l)return null;for(;d!==null;)t(m,d),d=d.sibling;return null}function a(m){for(var d=new Map;m!==null;)m.key!==null?d.set(m.key,m):d.set(m.index,m),m=m.sibling;return d}function u(m,d){return m=Bt(m,d),m.index=0,m.sibling=null,m}function n(m,d,h){return m.index=h,l?(h=m.alternate,h!==null?(h=h.index,h<d?(m.flags|=67108866,d):h):(m.flags|=67108866,d)):(m.flags|=1048576,d)}function i(m){return l&&m.alternate===null&&(m.flags|=67108866),m}function c(m,d,h,E){return d===null||d.tag!==6?(d=Ni(h,m.mode,E),d.return=m,d):(d=u(d,h),d.return=m,d)}function r(m,d,h,E){var B=h.type;return B===Z?A(m,d,h.props.children,E,h.key):d!==null&&(d.elementType===B||typeof B=="object"&&B!==null&&B.$$typeof===Q&&Or(B)===d.type)?(d=u(d,h.props),ru(d,h),d.return=m,d):(d=Iu(h.type,h.key,h.props,null,m.mode,E),ru(d,h),d.return=m,d)}function v(m,d,h,E){return d===null||d.tag!==4||d.stateNode.containerInfo!==h.containerInfo||d.stateNode.implementation!==h.implementation?(d=Ui(h,m.mode,E),d.return=m,d):(d=u(d,h.children||[]),d.return=m,d)}function A(m,d,h,E,B){return d===null||d.tag!==7?(d=Re(h,m.mode,E,B),d.return=m,d):(d=u(d,h),d.return=m,d)}function z(m,d,h){if(typeof d=="string"&&d!==""||typeof d=="number"||typeof d=="bigint")return d=Ni(""+d,m.mode,h),d.return=m,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case $:return h=Iu(d.type,d.key,d.props,null,m.mode,h),ru(h,d),h.return=m,h;case xl:return d=Ui(d,m.mode,h),d.return=m,d;case Q:var E=d._init;return d=E(d._payload),z(m,d,h)}if(Al(d)||Ol(d))return d=Re(d,m.mode,h,null),d.return=m,d;if(typeof d.then=="function")return z(m,gn(d),h);if(d.$$typeof===ml)return z(m,an(m,d),h);bn(m,d)}return null}function g(m,d,h,E){var B=d!==null?d.key:null;if(typeof h=="string"&&h!==""||typeof h=="number"||typeof h=="bigint")return B!==null?null:c(m,d,""+h,E);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case $:return h.key===B?r(m,d,h,E):null;case xl:return h.key===B?v(m,d,h,E):null;case Q:return B=h._init,h=B(h._payload),g(m,d,h,E)}if(Al(h)||Ol(h))return B!==null?null:A(m,d,h,E,null);if(typeof h.then=="function")return g(m,d,gn(h),E);if(h.$$typeof===ml)return g(m,d,an(m,h),E);bn(m,h)}return null}function b(m,d,h,E,B){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return m=m.get(h)||null,c(d,m,""+E,B);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case $:return m=m.get(E.key===null?h:E.key)||null,r(d,m,E,B);case xl:return m=m.get(E.key===null?h:E.key)||null,v(d,m,E,B);case Q:var tl=E._init;return E=tl(E._payload),b(m,d,h,E,B)}if(Al(E)||Ol(E))return m=m.get(h)||null,A(d,m,E,B,null);if(typeof E.then=="function")return b(m,d,h,gn(E),B);if(E.$$typeof===ml)return b(m,d,h,an(d,E),B);bn(d,E)}return null}function K(m,d,h,E){for(var B=null,tl=null,X=d,L=d=0,Ql=null;X!==null&&L<h.length;L++){X.index>L?(Ql=X,X=null):Ql=X.sibling;var fl=g(m,X,h[L],E);if(fl===null){X===null&&(X=Ql);break}l&&X&&fl.alternate===null&&t(m,X),d=n(fl,d,L),tl===null?B=fl:tl.sibling=fl,tl=fl,X=Ql}if(L===h.length)return e(m,X),sl&&Ue(m,L),B;if(X===null){for(;L<h.length;L++)X=z(m,h[L],E),X!==null&&(d=n(X,d,L),tl===null?B=X:tl.sibling=X,tl=X);return sl&&Ue(m,L),B}for(X=a(X);L<h.length;L++)Ql=b(X,m,L,h[L],E),Ql!==null&&(l&&Ql.alternate!==null&&X.delete(Ql.key===null?L:Ql.key),d=n(Ql,d,L),tl===null?B=Ql:tl.sibling=Ql,tl=Ql);return l&&X.forEach(function(Ae){return t(m,Ae)}),sl&&Ue(m,L),B}function V(m,d,h,E){if(h==null)throw Error(s(151));for(var B=null,tl=null,X=d,L=d=0,Ql=null,fl=h.next();X!==null&&!fl.done;L++,fl=h.next()){X.index>L?(Ql=X,X=null):Ql=X.sibling;var Ae=g(m,X,fl.value,E);if(Ae===null){X===null&&(X=Ql);break}l&&X&&Ae.alternate===null&&t(m,X),d=n(Ae,d,L),tl===null?B=Ae:tl.sibling=Ae,tl=Ae,X=Ql}if(fl.done)return e(m,X),sl&&Ue(m,L),B;if(X===null){for(;!fl.done;L++,fl=h.next())fl=z(m,fl.value,E),fl!==null&&(d=n(fl,d,L),tl===null?B=fl:tl.sibling=fl,tl=fl);return sl&&Ue(m,L),B}for(X=a(X);!fl.done;L++,fl=h.next())fl=b(X,m,L,fl.value,E),fl!==null&&(l&&fl.alternate!==null&&X.delete(fl.key===null?L:fl.key),d=n(fl,d,L),tl===null?B=fl:tl.sibling=fl,tl=fl);return l&&X.forEach(function(x0){return t(m,x0)}),sl&&Ue(m,L),B}function yl(m,d,h,E){if(typeof h=="object"&&h!==null&&h.type===Z&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case $:l:{for(var B=h.key;d!==null;){if(d.key===B){if(B=h.type,B===Z){if(d.tag===7){e(m,d.sibling),E=u(d,h.props.children),E.return=m,m=E;break l}}else if(d.elementType===B||typeof B=="object"&&B!==null&&B.$$typeof===Q&&Or(B)===d.type){e(m,d.sibling),E=u(d,h.props),ru(E,h),E.return=m,m=E;break l}e(m,d);break}else t(m,d);d=d.sibling}h.type===Z?(E=Re(h.props.children,m.mode,E,h.key),E.return=m,m=E):(E=Iu(h.type,h.key,h.props,null,m.mode,E),ru(E,h),E.return=m,m=E)}return i(m);case xl:l:{for(B=h.key;d!==null;){if(d.key===B)if(d.tag===4&&d.stateNode.containerInfo===h.containerInfo&&d.stateNode.implementation===h.implementation){e(m,d.sibling),E=u(d,h.children||[]),E.return=m,m=E;break l}else{e(m,d);break}else t(m,d);d=d.sibling}E=Ui(h,m.mode,E),E.return=m,m=E}return i(m);case Q:return B=h._init,h=B(h._payload),yl(m,d,h,E)}if(Al(h))return K(m,d,h,E);if(Ol(h)){if(B=Ol(h),typeof B!="function")throw Error(s(150));return h=B.call(h),V(m,d,h,E)}if(typeof h.then=="function")return yl(m,d,gn(h),E);if(h.$$typeof===ml)return yl(m,d,an(m,h),E);bn(m,h)}return typeof h=="string"&&h!==""||typeof h=="number"||typeof h=="bigint"?(h=""+h,d!==null&&d.tag===6?(e(m,d.sibling),E=u(d,h),E.return=m,m=E):(e(m,d),E=Ni(h,m.mode,E),E.return=m,m=E),i(m)):e(m,d)}return function(m,d,h,E){try{su=0;var B=yl(m,d,h,E);return va=null,B}catch(X){if(X===lu||X===nn)throw X;var tl=ct(29,X,null,m.mode);return tl.lanes=E,tl.return=m,tl}finally{}}}var ya=Dr(!0),Rr=Dr(!1),St=M(null),Rt=null;function fe(l){var t=l.alternate;N(ql,ql.current&1),N(St,l),Rt===null&&(t===null||oa.current!==null||t.memoizedState!==null)&&(Rt=l)}function Nr(l){if(l.tag===22){if(N(ql,ql.current),N(St,l),Rt===null){var t=l.alternate;t!==null&&t.memoizedState!==null&&(Rt=l)}}else se()}function se(){N(ql,ql.current),N(St,St.current)}function Zt(l){j(St),Rt===l&&(Rt=null),j(ql)}var ql=M(0);function pn(l){for(var t=l;t!==null;){if(t.tag===13){var e=t.memoizedState;if(e!==null&&(e=e.dehydrated,e===null||e.data==="$?"||lf(e)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===l)break;for(;t.sibling===null;){if(t.return===null||t.return===l)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function oc(l,t,e,a){t=l.memoizedState,e=e(a,t),e=e==null?t:R({},t,e),l.memoizedState=e,l.lanes===0&&(l.updateQueue.baseState=e)}var dc={enqueueSetState:function(l,t,e){l=l._reactInternals;var a=ot(),u=ne(a);u.payload=t,e!=null&&(u.callback=e),t=ie(l,u,a),t!==null&&(dt(t,l,a),eu(t,l,a))},enqueueReplaceState:function(l,t,e){l=l._reactInternals;var a=ot(),u=ne(a);u.tag=1,u.payload=t,e!=null&&(u.callback=e),t=ie(l,u,a),t!==null&&(dt(t,l,a),eu(t,l,a))},enqueueForceUpdate:function(l,t){l=l._reactInternals;var e=ot(),a=ne(e);a.tag=2,t!=null&&(a.callback=t),t=ie(l,a,e),t!==null&&(dt(t,l,e),eu(t,l,e))}};function Ur(l,t,e,a,u,n,i){return l=l.stateNode,typeof l.shouldComponentUpdate=="function"?l.shouldComponentUpdate(a,n,i):t.prototype&&t.prototype.isPureReactComponent?!Ka(e,a)||!Ka(u,n):!0}function Hr(l,t,e,a){l=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(e,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(e,a),t.state!==l&&dc.enqueueReplaceState(t,t.state,null)}function Ge(l,t){var e=t;if("ref"in t){e={};for(var a in t)a!=="ref"&&(e[a]=t[a])}if(l=l.defaultProps){e===t&&(e=R({},e));for(var u in l)e[u]===void 0&&(e[u]=l[u])}return e}var Sn=typeof reportError=="function"?reportError:function(l){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof l=="object"&&l!==null&&typeof l.message=="string"?String(l.message):String(l),error:l});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",l);return}console.error(l)};function jr(l){Sn(l)}function Cr(l){console.error(l)}function qr(l){Sn(l)}function xn(l,t){try{var e=l.onUncaughtError;e(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function Br(l,t,e){try{var a=l.onCaughtError;a(e.value,{componentStack:e.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function mc(l,t,e){return e=ne(e),e.tag=3,e.payload={element:null},e.callback=function(){xn(l,t)},e}function Yr(l){return l=ne(l),l.tag=3,l}function Gr(l,t,e,a){var u=e.type.getDerivedStateFromError;if(typeof u=="function"){var n=a.value;l.payload=function(){return u(n)},l.callback=function(){Br(t,e,a)}}var i=e.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(l.callback=function(){Br(t,e,a),typeof u!="function"&&(ve===null?ve=new Set([this]):ve.add(this));var c=a.stack;this.componentDidCatch(a.value,{componentStack:c!==null?c:""})})}function xh(l,t,e,a,u){if(e.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=e.alternate,t!==null&&Fa(t,e,u,!0),e=St.current,e!==null){switch(e.tag){case 13:return Rt===null?Bc():e.alternate===null&&_l===0&&(_l=3),e.flags&=-257,e.flags|=65536,e.lanes=u,a===Qi?e.flags|=16384:(t=e.updateQueue,t===null?e.updateQueue=new Set([a]):t.add(a),Gc(l,a,u)),!1;case 22:return e.flags|=65536,a===Qi?e.flags|=16384:(t=e.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},e.updateQueue=t):(e=t.retryQueue,e===null?t.retryQueue=new Set([a]):e.add(a)),Gc(l,a,u)),!1}throw Error(s(435,e.tag))}return Gc(l,a,u),Bc(),!1}if(sl)return t=St.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,a!==Ci&&(l=Error(s(422),{cause:a}),$a(yt(l,e)))):(a!==Ci&&(t=Error(s(423),{cause:a}),$a(yt(t,e))),l=l.current.alternate,l.flags|=65536,u&=-u,l.lanes|=u,a=yt(a,e),u=mc(l.stateNode,a,u),Li(l,u),_l!==4&&(_l=2)),!1;var n=Error(s(520),{cause:a});if(n=yt(n,e),gu===null?gu=[n]:gu.push(n),_l!==4&&(_l=2),t===null)return!0;a=yt(a,e),e=t;do{switch(e.tag){case 3:return e.flags|=65536,l=u&-u,e.lanes|=l,l=mc(e.stateNode,a,l),Li(e,l),!1;case 1:if(t=e.type,n=e.stateNode,(e.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||n!==null&&typeof n.componentDidCatch=="function"&&(ve===null||!ve.has(n))))return e.flags|=65536,u&=-u,e.lanes|=u,u=Yr(u),Gr(u,l,e,a),Li(e,u),!1}e=e.return}while(e!==null);return!1}var wr=Error(s(461)),wl=!1;function Ll(l,t,e,a){t.child=l===null?Rr(t,null,e,a):ya(t,l.child,e,a)}function Xr(l,t,e,a,u){e=e.render;var n=t.ref;if("ref"in a){var i={};for(var c in a)c!=="ref"&&(i[c]=a[c])}else i=a;return qe(t),a=$i(l,t,e,i,n,u),c=Fi(),l!==null&&!wl?(Pi(l,t,u),Vt(l,t,u)):(sl&&c&&Hi(t),t.flags|=1,Ll(l,t,a,u),t.child)}function Qr(l,t,e,a,u){if(l===null){var n=e.type;return typeof n=="function"&&!Ri(n)&&n.defaultProps===void 0&&e.compare===null?(t.tag=15,t.type=n,Zr(l,t,n,a,u)):(l=Iu(e.type,null,a,t,t.mode,u),l.ref=t.ref,l.return=t,t.child=l)}if(n=l.child,!xc(l,u)){var i=n.memoizedProps;if(e=e.compare,e=e!==null?e:Ka,e(i,a)&&l.ref===t.ref)return Vt(l,t,u)}return t.flags|=1,l=Bt(n,a),l.ref=t.ref,l.return=t,t.child=l}function Zr(l,t,e,a,u){if(l!==null){var n=l.memoizedProps;if(Ka(n,a)&&l.ref===t.ref)if(wl=!1,t.pendingProps=a=n,xc(l,u))(l.flags&131072)!==0&&(wl=!0);else return t.lanes=l.lanes,Vt(l,t,u)}return hc(l,t,e,a,u)}function Vr(l,t,e){var a=t.pendingProps,u=a.children,n=l!==null?l.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=n!==null?n.baseLanes|e:e,l!==null){for(u=t.child=l.child,n=0;u!==null;)n=n|u.lanes|u.childLanes,u=u.sibling;t.childLanes=n&~a}else t.childLanes=0,t.child=null;return Lr(l,t,a,e)}if((e&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},l!==null&&un(t,n!==null?n.cachePool:null),n!==null?Zs(t,n):ki(),Nr(t);else return t.lanes=t.childLanes=536870912,Lr(l,t,n!==null?n.baseLanes|e:e,e)}else n!==null?(un(t,n.cachePool),Zs(t,n),se(),t.memoizedState=null):(l!==null&&un(t,null),ki(),se());return Ll(l,t,u,e),t.child}function Lr(l,t,e,a){var u=Xi();return u=u===null?null:{parent:Cl._currentValue,pool:u},t.memoizedState={baseLanes:e,cachePool:u},l!==null&&un(t,null),ki(),Nr(t),l!==null&&Fa(l,t,a,!0),null}function Tn(l,t){var e=t.ref;if(e===null)l!==null&&l.ref!==null&&(t.flags|=4194816);else{if(typeof e!="function"&&typeof e!="object")throw Error(s(284));(l===null||l.ref!==e)&&(t.flags|=4194816)}}function hc(l,t,e,a,u){return qe(t),e=$i(l,t,e,a,void 0,u),a=Fi(),l!==null&&!wl?(Pi(l,t,u),Vt(l,t,u)):(sl&&a&&Hi(t),t.flags|=1,Ll(l,t,e,u),t.child)}function Kr(l,t,e,a,u,n){return qe(t),t.updateQueue=null,e=Ls(t,a,e,u),Vs(l),a=Fi(),l!==null&&!wl?(Pi(l,t,n),Vt(l,t,n)):(sl&&a&&Hi(t),t.flags|=1,Ll(l,t,e,n),t.child)}function kr(l,t,e,a,u){if(qe(t),t.stateNode===null){var n=ia,i=e.contextType;typeof i=="object"&&i!==null&&(n=Wl(i)),n=new e(a,n),t.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,n.updater=dc,t.stateNode=n,n._reactInternals=t,n=t.stateNode,n.props=a,n.state=t.memoizedState,n.refs={},Zi(t),i=e.contextType,n.context=typeof i=="object"&&i!==null?Wl(i):ia,n.state=t.memoizedState,i=e.getDerivedStateFromProps,typeof i=="function"&&(oc(t,e,i,a),n.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof n.getSnapshotBeforeUpdate=="function"||typeof n.UNSAFE_componentWillMount!="function"&&typeof n.componentWillMount!="function"||(i=n.state,typeof n.componentWillMount=="function"&&n.componentWillMount(),typeof n.UNSAFE_componentWillMount=="function"&&n.UNSAFE_componentWillMount(),i!==n.state&&dc.enqueueReplaceState(n,n.state,null),uu(t,a,n,u),au(),n.state=t.memoizedState),typeof n.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(l===null){n=t.stateNode;var c=t.memoizedProps,r=Ge(e,c);n.props=r;var v=n.context,A=e.contextType;i=ia,typeof A=="object"&&A!==null&&(i=Wl(A));var z=e.getDerivedStateFromProps;A=typeof z=="function"||typeof n.getSnapshotBeforeUpdate=="function",c=t.pendingProps!==c,A||typeof n.UNSAFE_componentWillReceiveProps!="function"&&typeof n.componentWillReceiveProps!="function"||(c||v!==i)&&Hr(t,n,a,i),ue=!1;var g=t.memoizedState;n.state=g,uu(t,a,n,u),au(),v=t.memoizedState,c||g!==v||ue?(typeof z=="function"&&(oc(t,e,z,a),v=t.memoizedState),(r=ue||Ur(t,e,r,a,g,v,i))?(A||typeof n.UNSAFE_componentWillMount!="function"&&typeof n.componentWillMount!="function"||(typeof n.componentWillMount=="function"&&n.componentWillMount(),typeof n.UNSAFE_componentWillMount=="function"&&n.UNSAFE_componentWillMount()),typeof n.componentDidMount=="function"&&(t.flags|=4194308)):(typeof n.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=v),n.props=a,n.state=v,n.context=i,a=r):(typeof n.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{n=t.stateNode,Vi(l,t),i=t.memoizedProps,A=Ge(e,i),n.props=A,z=t.pendingProps,g=n.context,v=e.contextType,r=ia,typeof v=="object"&&v!==null&&(r=Wl(v)),c=e.getDerivedStateFromProps,(v=typeof c=="function"||typeof n.getSnapshotBeforeUpdate=="function")||typeof n.UNSAFE_componentWillReceiveProps!="function"&&typeof n.componentWillReceiveProps!="function"||(i!==z||g!==r)&&Hr(t,n,a,r),ue=!1,g=t.memoizedState,n.state=g,uu(t,a,n,u),au();var b=t.memoizedState;i!==z||g!==b||ue||l!==null&&l.dependencies!==null&&en(l.dependencies)?(typeof c=="function"&&(oc(t,e,c,a),b=t.memoizedState),(A=ue||Ur(t,e,A,a,g,b,r)||l!==null&&l.dependencies!==null&&en(l.dependencies))?(v||typeof n.UNSAFE_componentWillUpdate!="function"&&typeof n.componentWillUpdate!="function"||(typeof n.componentWillUpdate=="function"&&n.componentWillUpdate(a,b,r),typeof n.UNSAFE_componentWillUpdate=="function"&&n.UNSAFE_componentWillUpdate(a,b,r)),typeof n.componentDidUpdate=="function"&&(t.flags|=4),typeof n.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof n.componentDidUpdate!="function"||i===l.memoizedProps&&g===l.memoizedState||(t.flags|=4),typeof n.getSnapshotBeforeUpdate!="function"||i===l.memoizedProps&&g===l.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=b),n.props=a,n.state=b,n.context=r,a=A):(typeof n.componentDidUpdate!="function"||i===l.memoizedProps&&g===l.memoizedState||(t.flags|=4),typeof n.getSnapshotBeforeUpdate!="function"||i===l.memoizedProps&&g===l.memoizedState||(t.flags|=1024),a=!1)}return n=a,Tn(l,t),a=(t.flags&128)!==0,n||a?(n=t.stateNode,e=a&&typeof e.getDerivedStateFromError!="function"?null:n.render(),t.flags|=1,l!==null&&a?(t.child=ya(t,l.child,null,u),t.child=ya(t,null,e,u)):Ll(l,t,e,u),t.memoizedState=n.state,l=t.child):l=Vt(l,t,u),l}function Jr(l,t,e,a){return Wa(),t.flags|=256,Ll(l,t,e,a),t.child}var vc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function yc(l){return{baseLanes:l,cachePool:Cs()}}function gc(l,t,e){return l=l!==null?l.childLanes&~e:0,t&&(l|=xt),l}function Wr(l,t,e){var a=t.pendingProps,u=!1,n=(t.flags&128)!==0,i;if((i=n)||(i=l!==null&&l.memoizedState===null?!1:(ql.current&2)!==0),i&&(u=!0,t.flags&=-129),i=(t.flags&32)!==0,t.flags&=-33,l===null){if(sl){if(u?fe(t):se(),sl){var c=zl,r;if(r=c){l:{for(r=c,c=Dt;r.nodeType!==8;){if(!c){c=null;break l}if(r=_t(r.nextSibling),r===null){c=null;break l}}c=r}c!==null?(t.memoizedState={dehydrated:c,treeContext:Ne!==null?{id:Yt,overflow:Gt}:null,retryLane:536870912,hydrationErrors:null},r=ct(18,null,null,0),r.stateNode=c,r.return=t,t.child=r,Fl=t,zl=null,r=!0):r=!1}r||je(t)}if(c=t.memoizedState,c!==null&&(c=c.dehydrated,c!==null))return lf(c)?t.lanes=32:t.lanes=536870912,null;Zt(t)}return c=a.children,a=a.fallback,u?(se(),u=t.mode,c=An({mode:"hidden",children:c},u),a=Re(a,u,e,null),c.return=t,a.return=t,c.sibling=a,t.child=c,u=t.child,u.memoizedState=yc(e),u.childLanes=gc(l,i,e),t.memoizedState=vc,a):(fe(t),bc(t,c))}if(r=l.memoizedState,r!==null&&(c=r.dehydrated,c!==null)){if(n)t.flags&256?(fe(t),t.flags&=-257,t=pc(l,t,e)):t.memoizedState!==null?(se(),t.child=l.child,t.flags|=128,t=null):(se(),u=a.fallback,c=t.mode,a=An({mode:"visible",children:a.children},c),u=Re(u,c,e,null),u.flags|=2,a.return=t,u.return=t,a.sibling=u,t.child=a,ya(t,l.child,null,e),a=t.child,a.memoizedState=yc(e),a.childLanes=gc(l,i,e),t.memoizedState=vc,t=u);else if(fe(t),lf(c)){if(i=c.nextSibling&&c.nextSibling.dataset,i)var v=i.dgst;i=v,a=Error(s(419)),a.stack="",a.digest=i,$a({value:a,source:null,stack:null}),t=pc(l,t,e)}else if(wl||Fa(l,t,e,!1),i=(e&l.childLanes)!==0,wl||i){if(i=Sl,i!==null&&(a=e&-e,a=(a&42)!==0?1:ti(a),a=(a&(i.suspendedLanes|e))!==0?0:a,a!==0&&a!==r.retryLane))throw r.retryLane=a,na(l,a),dt(i,l,a),wr;c.data==="$?"||Bc(),t=pc(l,t,e)}else c.data==="$?"?(t.flags|=192,t.child=l.child,t=null):(l=r.treeContext,zl=_t(c.nextSibling),Fl=t,sl=!0,He=null,Dt=!1,l!==null&&(bt[pt++]=Yt,bt[pt++]=Gt,bt[pt++]=Ne,Yt=l.id,Gt=l.overflow,Ne=t),t=bc(t,a.children),t.flags|=4096);return t}return u?(se(),u=a.fallback,c=t.mode,r=l.child,v=r.sibling,a=Bt(r,{mode:"hidden",children:a.children}),a.subtreeFlags=r.subtreeFlags&65011712,v!==null?u=Bt(v,u):(u=Re(u,c,e,null),u.flags|=2),u.return=t,a.return=t,a.sibling=u,t.child=a,a=u,u=t.child,c=l.child.memoizedState,c===null?c=yc(e):(r=c.cachePool,r!==null?(v=Cl._currentValue,r=r.parent!==v?{parent:v,pool:v}:r):r=Cs(),c={baseLanes:c.baseLanes|e,cachePool:r}),u.memoizedState=c,u.childLanes=gc(l,i,e),t.memoizedState=vc,a):(fe(t),e=l.child,l=e.sibling,e=Bt(e,{mode:"visible",children:a.children}),e.return=t,e.sibling=null,l!==null&&(i=t.deletions,i===null?(t.deletions=[l],t.flags|=16):i.push(l)),t.child=e,t.memoizedState=null,e)}function bc(l,t){return t=An({mode:"visible",children:t},l.mode),t.return=l,l.child=t}function An(l,t){return l=ct(22,l,null,t),l.lanes=0,l.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},l}function pc(l,t,e){return ya(t,l.child,null,e),l=bc(t,t.pendingProps.children),l.flags|=2,t.memoizedState=null,l}function $r(l,t,e){l.lanes|=t;var a=l.alternate;a!==null&&(a.lanes|=t),Bi(l.return,t,e)}function Sc(l,t,e,a,u){var n=l.memoizedState;n===null?l.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:e,tailMode:u}:(n.isBackwards=t,n.rendering=null,n.renderingStartTime=0,n.last=a,n.tail=e,n.tailMode=u)}function Fr(l,t,e){var a=t.pendingProps,u=a.revealOrder,n=a.tail;if(Ll(l,t,a.children,e),a=ql.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(l!==null&&(l.flags&128)!==0)l:for(l=t.child;l!==null;){if(l.tag===13)l.memoizedState!==null&&$r(l,e,t);else if(l.tag===19)$r(l,e,t);else if(l.child!==null){l.child.return=l,l=l.child;continue}if(l===t)break l;for(;l.sibling===null;){if(l.return===null||l.return===t)break l;l=l.return}l.sibling.return=l.return,l=l.sibling}a&=1}switch(N(ql,a),u){case"forwards":for(e=t.child,u=null;e!==null;)l=e.alternate,l!==null&&pn(l)===null&&(u=e),e=e.sibling;e=u,e===null?(u=t.child,t.child=null):(u=e.sibling,e.sibling=null),Sc(t,!1,u,e,n);break;case"backwards":for(e=null,u=t.child,t.child=null;u!==null;){if(l=u.alternate,l!==null&&pn(l)===null){t.child=u;break}l=u.sibling,u.sibling=e,e=u,u=l}Sc(t,!0,e,null,n);break;case"together":Sc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vt(l,t,e){if(l!==null&&(t.dependencies=l.dependencies),he|=t.lanes,(e&t.childLanes)===0)if(l!==null){if(Fa(l,t,e,!1),(e&t.childLanes)===0)return null}else return null;if(l!==null&&t.child!==l.child)throw Error(s(153));if(t.child!==null){for(l=t.child,e=Bt(l,l.pendingProps),t.child=e,e.return=t;l.sibling!==null;)l=l.sibling,e=e.sibling=Bt(l,l.pendingProps),e.return=t;e.sibling=null}return t.child}function xc(l,t){return(l.lanes&t)!==0?!0:(l=l.dependencies,!!(l!==null&&en(l)))}function Th(l,t,e){switch(t.tag){case 3:gl(t,t.stateNode.containerInfo),ae(t,Cl,l.memoizedState.cache),Wa();break;case 27:case 5:Ft(t);break;case 4:gl(t,t.stateNode.containerInfo);break;case 10:ae(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(fe(t),t.flags|=128,null):(e&t.child.childLanes)!==0?Wr(l,t,e):(fe(t),l=Vt(l,t,e),l!==null?l.sibling:null);fe(t);break;case 19:var u=(l.flags&128)!==0;if(a=(e&t.childLanes)!==0,a||(Fa(l,t,e,!1),a=(e&t.childLanes)!==0),u){if(a)return Fr(l,t,e);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),N(ql,ql.current),a)break;return null;case 22:case 23:return t.lanes=0,Vr(l,t,e);case 24:ae(t,Cl,l.memoizedState.cache)}return Vt(l,t,e)}function Pr(l,t,e){if(l!==null)if(l.memoizedProps!==t.pendingProps)wl=!0;else{if(!xc(l,e)&&(t.flags&128)===0)return wl=!1,Th(l,t,e);wl=(l.flags&131072)!==0}else wl=!1,sl&&(t.flags&1048576)!==0&&Os(t,tn,t.index);switch(t.lanes=0,t.tag){case 16:l:{l=t.pendingProps;var a=t.elementType,u=a._init;if(a=u(a._payload),t.type=a,typeof a=="function")Ri(a)?(l=Ge(a,l),t.tag=1,t=kr(null,t,a,l,e)):(t.tag=0,t=hc(null,t,a,l,e));else{if(a!=null){if(u=a.$$typeof,u===Yl){t.tag=11,t=Xr(null,t,a,l,e);break l}else if(u===jl){t.tag=14,t=Qr(null,t,a,l,e);break l}}throw t=At(a)||a,Error(s(306,t,""))}}return t;case 0:return hc(l,t,t.type,t.pendingProps,e);case 1:return a=t.type,u=Ge(a,t.pendingProps),kr(l,t,a,u,e);case 3:l:{if(gl(t,t.stateNode.containerInfo),l===null)throw Error(s(387));a=t.pendingProps;var n=t.memoizedState;u=n.element,Vi(l,t),uu(t,a,null,e);var i=t.memoizedState;if(a=i.cache,ae(t,Cl,a),a!==n.cache&&Yi(t,[Cl],e,!0),au(),a=i.element,n.isDehydrated)if(n={element:a,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=n,t.memoizedState=n,t.flags&256){t=Jr(l,t,a,e);break l}else if(a!==u){u=yt(Error(s(424)),t),$a(u),t=Jr(l,t,a,e);break l}else{switch(l=t.stateNode.containerInfo,l.nodeType){case 9:l=l.body;break;default:l=l.nodeName==="HTML"?l.ownerDocument.body:l}for(zl=_t(l.firstChild),Fl=t,sl=!0,He=null,Dt=!0,e=Rr(t,null,a,e),t.child=e;e;)e.flags=e.flags&-3|4096,e=e.sibling}else{if(Wa(),a===u){t=Vt(l,t,e);break l}Ll(l,t,a,e)}t=t.child}return t;case 26:return Tn(l,t),l===null?(e=ad(t.type,null,t.pendingProps,null))?t.memoizedState=e:sl||(e=t.type,l=t.pendingProps,a=Bn(k.current).createElement(e),a[Jl]=t,a[Pl]=l,kl(a,e,l),Gl(a),t.stateNode=a):t.memoizedState=ad(t.type,l.memoizedProps,t.pendingProps,l.memoizedState),null;case 27:return Ft(t),l===null&&sl&&(a=t.stateNode=ld(t.type,t.pendingProps,k.current),Fl=t,Dt=!0,u=zl,be(t.type)?(tf=u,zl=_t(a.firstChild)):zl=u),Ll(l,t,t.pendingProps.children,e),Tn(l,t),l===null&&(t.flags|=4194304),t.child;case 5:return l===null&&sl&&((u=a=zl)&&(a=$h(a,t.type,t.pendingProps,Dt),a!==null?(t.stateNode=a,Fl=t,zl=_t(a.firstChild),Dt=!1,u=!0):u=!1),u||je(t)),Ft(t),u=t.type,n=t.pendingProps,i=l!==null?l.memoizedProps:null,a=n.children,Fc(u,n)?a=null:i!==null&&Fc(u,i)&&(t.flags|=32),t.memoizedState!==null&&(u=$i(l,t,hh,null,null,e),_u._currentValue=u),Tn(l,t),Ll(l,t,a,e),t.child;case 6:return l===null&&sl&&((l=e=zl)&&(e=Fh(e,t.pendingProps,Dt),e!==null?(t.stateNode=e,Fl=t,zl=null,l=!0):l=!1),l||je(t)),null;case 13:return Wr(l,t,e);case 4:return gl(t,t.stateNode.containerInfo),a=t.pendingProps,l===null?t.child=ya(t,null,a,e):Ll(l,t,a,e),t.child;case 11:return Xr(l,t,t.type,t.pendingProps,e);case 7:return Ll(l,t,t.pendingProps,e),t.child;case 8:return Ll(l,t,t.pendingProps.children,e),t.child;case 12:return Ll(l,t,t.pendingProps.children,e),t.child;case 10:return a=t.pendingProps,ae(t,t.type,a.value),Ll(l,t,a.children,e),t.child;case 9:return u=t.type._context,a=t.pendingProps.children,qe(t),u=Wl(u),a=a(u),t.flags|=1,Ll(l,t,a,e),t.child;case 14:return Qr(l,t,t.type,t.pendingProps,e);case 15:return Zr(l,t,t.type,t.pendingProps,e);case 19:return Fr(l,t,e);case 31:return a=t.pendingProps,e=t.mode,a={mode:a.mode,children:a.children},l===null?(e=An(a,e),e.ref=t.ref,t.child=e,e.return=t,t=e):(e=Bt(l.child,a),e.ref=t.ref,t.child=e,e.return=t,t=e),t;case 22:return Vr(l,t,e);case 24:return qe(t),a=Wl(Cl),l===null?(u=Xi(),u===null&&(u=Sl,n=Gi(),u.pooledCache=n,n.refCount++,n!==null&&(u.pooledCacheLanes|=e),u=n),t.memoizedState={parent:a,cache:u},Zi(t),ae(t,Cl,u)):((l.lanes&e)!==0&&(Vi(l,t),uu(t,null,null,e),au()),u=l.memoizedState,n=t.memoizedState,u.parent!==a?(u={parent:a,cache:a},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),ae(t,Cl,a)):(a=n.cache,ae(t,Cl,a),a!==u.cache&&Yi(t,[Cl],e,!0))),Ll(l,t,t.pendingProps.children,e),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function Lt(l){l.flags|=4}function Ir(l,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)l.flags&=-16777217;else if(l.flags|=16777216,!fd(t)){if(t=St.current,t!==null&&((il&4194048)===il?Rt!==null:(il&62914560)!==il&&(il&536870912)===0||t!==Rt))throw tu=Qi,qs;l.flags|=8192}}function En(l,t){t!==null&&(l.flags|=4),l.flags&16384&&(t=l.tag!==22?Nf():536870912,l.lanes|=t,Sa|=t)}function ou(l,t){if(!sl)switch(l.tailMode){case"hidden":t=l.tail;for(var e=null;t!==null;)t.alternate!==null&&(e=t),t=t.sibling;e===null?l.tail=null:e.sibling=null;break;case"collapsed":e=l.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?t||l.tail===null?l.tail=null:l.tail.sibling=null:a.sibling=null}}function El(l){var t=l.alternate!==null&&l.alternate.child===l.child,e=0,a=0;if(t)for(var u=l.child;u!==null;)e|=u.lanes|u.childLanes,a|=u.subtreeFlags&65011712,a|=u.flags&65011712,u.return=l,u=u.sibling;else for(u=l.child;u!==null;)e|=u.lanes|u.childLanes,a|=u.subtreeFlags,a|=u.flags,u.return=l,u=u.sibling;return l.subtreeFlags|=a,l.childLanes=e,t}function Ah(l,t,e){var a=t.pendingProps;switch(ji(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return El(t),null;case 1:return El(t),null;case 3:return e=t.stateNode,a=null,l!==null&&(a=l.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Xt(Cl),at(),e.pendingContext&&(e.context=e.pendingContext,e.pendingContext=null),(l===null||l.child===null)&&(Ja(t)?Lt(t):l===null||l.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Ns())),El(t),null;case 26:return e=t.memoizedState,l===null?(Lt(t),e!==null?(El(t),Ir(t,e)):(El(t),t.flags&=-16777217)):e?e!==l.memoizedState?(Lt(t),El(t),Ir(t,e)):(El(t),t.flags&=-16777217):(l.memoizedProps!==a&&Lt(t),El(t),t.flags&=-16777217),null;case 27:Pt(t),e=k.current;var u=t.type;if(l!==null&&t.stateNode!=null)l.memoizedProps!==a&&Lt(t);else{if(!a){if(t.stateNode===null)throw Error(s(166));return El(t),null}l=q.current,Ja(t)?Ds(t):(l=ld(u,a,e),t.stateNode=l,Lt(t))}return El(t),null;case 5:if(Pt(t),e=t.type,l!==null&&t.stateNode!=null)l.memoizedProps!==a&&Lt(t);else{if(!a){if(t.stateNode===null)throw Error(s(166));return El(t),null}if(l=q.current,Ja(t))Ds(t);else{switch(u=Bn(k.current),l){case 1:l=u.createElementNS("http://www.w3.org/2000/svg",e);break;case 2:l=u.createElementNS("http://www.w3.org/1998/Math/MathML",e);break;default:switch(e){case"svg":l=u.createElementNS("http://www.w3.org/2000/svg",e);break;case"math":l=u.createElementNS("http://www.w3.org/1998/Math/MathML",e);break;case"script":l=u.createElement("div"),l.innerHTML="<script><\/script>",l=l.removeChild(l.firstChild);break;case"select":l=typeof a.is=="string"?u.createElement("select",{is:a.is}):u.createElement("select"),a.multiple?l.multiple=!0:a.size&&(l.size=a.size);break;default:l=typeof a.is=="string"?u.createElement(e,{is:a.is}):u.createElement(e)}}l[Jl]=t,l[Pl]=a;l:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)l.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break l;for(;u.sibling===null;){if(u.return===null||u.return===t)break l;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=l;l:switch(kl(l,e,a),e){case"button":case"input":case"select":case"textarea":l=!!a.autoFocus;break l;case"img":l=!0;break l;default:l=!1}l&&Lt(t)}}return El(t),t.flags&=-16777217,null;case 6:if(l&&t.stateNode!=null)l.memoizedProps!==a&&Lt(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(s(166));if(l=k.current,Ja(t)){if(l=t.stateNode,e=t.memoizedProps,a=null,u=Fl,u!==null)switch(u.tag){case 27:case 5:a=u.memoizedProps}l[Jl]=t,l=!!(l.nodeValue===e||a!==null&&a.suppressHydrationWarning===!0||ko(l.nodeValue,e)),l||je(t)}else l=Bn(l).createTextNode(a),l[Jl]=t,t.stateNode=l}return El(t),null;case 13:if(a=t.memoizedState,l===null||l.memoizedState!==null&&l.memoizedState.dehydrated!==null){if(u=Ja(t),a!==null&&a.dehydrated!==null){if(l===null){if(!u)throw Error(s(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(s(317));u[Jl]=t}else Wa(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;El(t),u=!1}else u=Ns(),l!==null&&l.memoizedState!==null&&(l.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(Zt(t),t):(Zt(t),null)}if(Zt(t),(t.flags&128)!==0)return t.lanes=e,t;if(e=a!==null,l=l!==null&&l.memoizedState!==null,e){a=t.child,u=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(u=a.alternate.memoizedState.cachePool.pool);var n=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(n=a.memoizedState.cachePool.pool),n!==u&&(a.flags|=2048)}return e!==l&&e&&(t.child.flags|=8192),En(t,t.updateQueue),El(t),null;case 4:return at(),l===null&&Kc(t.stateNode.containerInfo),El(t),null;case 10:return Xt(t.type),El(t),null;case 19:if(j(ql),u=t.memoizedState,u===null)return El(t),null;if(a=(t.flags&128)!==0,n=u.rendering,n===null)if(a)ou(u,!1);else{if(_l!==0||l!==null&&(l.flags&128)!==0)for(l=t.child;l!==null;){if(n=pn(l),n!==null){for(t.flags|=128,ou(u,!1),l=n.updateQueue,t.updateQueue=l,En(t,l),t.subtreeFlags=0,l=e,e=t.child;e!==null;)Ms(e,l),e=e.sibling;return N(ql,ql.current&1|2),t.child}l=l.sibling}u.tail!==null&&Ot()>Mn&&(t.flags|=128,a=!0,ou(u,!1),t.lanes=4194304)}else{if(!a)if(l=pn(n),l!==null){if(t.flags|=128,a=!0,l=l.updateQueue,t.updateQueue=l,En(t,l),ou(u,!0),u.tail===null&&u.tailMode==="hidden"&&!n.alternate&&!sl)return El(t),null}else 2*Ot()-u.renderingStartTime>Mn&&e!==536870912&&(t.flags|=128,a=!0,ou(u,!1),t.lanes=4194304);u.isBackwards?(n.sibling=t.child,t.child=n):(l=u.last,l!==null?l.sibling=n:t.child=n,u.last=n)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=Ot(),t.sibling=null,l=ql.current,N(ql,a?l&1|2:l&1),t):(El(t),null);case 22:case 23:return Zt(t),Ji(),a=t.memoizedState!==null,l!==null?l.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(e&536870912)!==0&&(t.flags&128)===0&&(El(t),t.subtreeFlags&6&&(t.flags|=8192)):El(t),e=t.updateQueue,e!==null&&En(t,e.retryQueue),e=null,l!==null&&l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(e=l.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==e&&(t.flags|=2048),l!==null&&j(Be),null;case 24:return e=null,l!==null&&(e=l.memoizedState.cache),t.memoizedState.cache!==e&&(t.flags|=2048),Xt(Cl),El(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function Eh(l,t){switch(ji(t),t.tag){case 1:return l=t.flags,l&65536?(t.flags=l&-65537|128,t):null;case 3:return Xt(Cl),at(),l=t.flags,(l&65536)!==0&&(l&128)===0?(t.flags=l&-65537|128,t):null;case 26:case 27:case 5:return Pt(t),null;case 13:if(Zt(t),l=t.memoizedState,l!==null&&l.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Wa()}return l=t.flags,l&65536?(t.flags=l&-65537|128,t):null;case 19:return j(ql),null;case 4:return at(),null;case 10:return Xt(t.type),null;case 22:case 23:return Zt(t),Ji(),l!==null&&j(Be),l=t.flags,l&65536?(t.flags=l&-65537|128,t):null;case 24:return Xt(Cl),null;case 25:return null;default:return null}}function lo(l,t){switch(ji(t),t.tag){case 3:Xt(Cl),at();break;case 26:case 27:case 5:Pt(t);break;case 4:at();break;case 13:Zt(t);break;case 19:j(ql);break;case 10:Xt(t.type);break;case 22:case 23:Zt(t),Ji(),l!==null&&j(Be);break;case 24:Xt(Cl)}}function du(l,t){try{var e=t.updateQueue,a=e!==null?e.lastEffect:null;if(a!==null){var u=a.next;e=u;do{if((e.tag&l)===l){a=void 0;var n=e.create,i=e.inst;a=n(),i.destroy=a}e=e.next}while(e!==u)}}catch(c){bl(t,t.return,c)}}function re(l,t,e){try{var a=t.updateQueue,u=a!==null?a.lastEffect:null;if(u!==null){var n=u.next;a=n;do{if((a.tag&l)===l){var i=a.inst,c=i.destroy;if(c!==void 0){i.destroy=void 0,u=t;var r=e,v=c;try{v()}catch(A){bl(u,r,A)}}}a=a.next}while(a!==n)}}catch(A){bl(t,t.return,A)}}function to(l){var t=l.updateQueue;if(t!==null){var e=l.stateNode;try{Qs(t,e)}catch(a){bl(l,l.return,a)}}}function eo(l,t,e){e.props=Ge(l.type,l.memoizedProps),e.state=l.memoizedState;try{e.componentWillUnmount()}catch(a){bl(l,t,a)}}function mu(l,t){try{var e=l.ref;if(e!==null){switch(l.tag){case 26:case 27:case 5:var a=l.stateNode;break;case 30:a=l.stateNode;break;default:a=l.stateNode}typeof e=="function"?l.refCleanup=e(a):e.current=a}}catch(u){bl(l,t,u)}}function Nt(l,t){var e=l.ref,a=l.refCleanup;if(e!==null)if(typeof a=="function")try{a()}catch(u){bl(l,t,u)}finally{l.refCleanup=null,l=l.alternate,l!=null&&(l.refCleanup=null)}else if(typeof e=="function")try{e(null)}catch(u){bl(l,t,u)}else e.current=null}function ao(l){var t=l.type,e=l.memoizedProps,a=l.stateNode;try{l:switch(t){case"button":case"input":case"select":case"textarea":e.autoFocus&&a.focus();break l;case"img":e.src?a.src=e.src:e.srcSet&&(a.srcset=e.srcSet)}}catch(u){bl(l,l.return,u)}}function Tc(l,t,e){try{var a=l.stateNode;Lh(a,l.type,e,t),a[Pl]=t}catch(u){bl(l,l.return,u)}}function uo(l){return l.tag===5||l.tag===3||l.tag===26||l.tag===27&&be(l.type)||l.tag===4}function Ac(l){l:for(;;){for(;l.sibling===null;){if(l.return===null||uo(l.return))return null;l=l.return}for(l.sibling.return=l.return,l=l.sibling;l.tag!==5&&l.tag!==6&&l.tag!==18;){if(l.tag===27&&be(l.type)||l.flags&2||l.child===null||l.tag===4)continue l;l.child.return=l,l=l.child}if(!(l.flags&2))return l.stateNode}}function Ec(l,t,e){var a=l.tag;if(a===5||a===6)l=l.stateNode,t?(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e).insertBefore(l,t):(t=e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,t.appendChild(l),e=e._reactRootContainer,e!=null||t.onclick!==null||(t.onclick=qn));else if(a!==4&&(a===27&&be(l.type)&&(e=l.stateNode,t=null),l=l.child,l!==null))for(Ec(l,t,e),l=l.sibling;l!==null;)Ec(l,t,e),l=l.sibling}function zn(l,t,e){var a=l.tag;if(a===5||a===6)l=l.stateNode,t?e.insertBefore(l,t):e.appendChild(l);else if(a!==4&&(a===27&&be(l.type)&&(e=l.stateNode),l=l.child,l!==null))for(zn(l,t,e),l=l.sibling;l!==null;)zn(l,t,e),l=l.sibling}function no(l){var t=l.stateNode,e=l.memoizedProps;try{for(var a=l.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);kl(t,a,e),t[Jl]=l,t[Pl]=e}catch(n){bl(l,l.return,n)}}var Kt=!1,Rl=!1,zc=!1,io=typeof WeakSet=="function"?WeakSet:Set,Xl=null;function zh(l,t){if(l=l.containerInfo,Wc=Zn,l=gs(l),Ai(l)){if("selectionStart"in l)var e={start:l.selectionStart,end:l.selectionEnd};else l:{e=(e=l.ownerDocument)&&e.defaultView||window;var a=e.getSelection&&e.getSelection();if(a&&a.rangeCount!==0){e=a.anchorNode;var u=a.anchorOffset,n=a.focusNode;a=a.focusOffset;try{e.nodeType,n.nodeType}catch{e=null;break l}var i=0,c=-1,r=-1,v=0,A=0,z=l,g=null;t:for(;;){for(var b;z!==e||u!==0&&z.nodeType!==3||(c=i+u),z!==n||a!==0&&z.nodeType!==3||(r=i+a),z.nodeType===3&&(i+=z.nodeValue.length),(b=z.firstChild)!==null;)g=z,z=b;for(;;){if(z===l)break t;if(g===e&&++v===u&&(c=i),g===n&&++A===a&&(r=i),(b=z.nextSibling)!==null)break;z=g,g=z.parentNode}z=b}e=c===-1||r===-1?null:{start:c,end:r}}else e=null}e=e||{start:0,end:0}}else e=null;for($c={focusedElem:l,selectionRange:e},Zn=!1,Xl=t;Xl!==null;)if(t=Xl,l=t.child,(t.subtreeFlags&1024)!==0&&l!==null)l.return=t,Xl=l;else for(;Xl!==null;){switch(t=Xl,n=t.alternate,l=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((l&1024)!==0&&n!==null){l=void 0,e=t,u=n.memoizedProps,n=n.memoizedState,a=e.stateNode;try{var K=Ge(e.type,u,e.elementType===e.type);l=a.getSnapshotBeforeUpdate(K,n),a.__reactInternalSnapshotBeforeUpdate=l}catch(V){bl(e,e.return,V)}}break;case 3:if((l&1024)!==0){if(l=t.stateNode.containerInfo,e=l.nodeType,e===9)Ic(l);else if(e===1)switch(l.nodeName){case"HEAD":case"HTML":case"BODY":Ic(l);break;default:l.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((l&1024)!==0)throw Error(s(163))}if(l=t.sibling,l!==null){l.return=t.return,Xl=l;break}Xl=t.return}}function co(l,t,e){var a=e.flags;switch(e.tag){case 0:case 11:case 15:oe(l,e),a&4&&du(5,e);break;case 1:if(oe(l,e),a&4)if(l=e.stateNode,t===null)try{l.componentDidMount()}catch(i){bl(e,e.return,i)}else{var u=Ge(e.type,t.memoizedProps);t=t.memoizedState;try{l.componentDidUpdate(u,t,l.__reactInternalSnapshotBeforeUpdate)}catch(i){bl(e,e.return,i)}}a&64&&to(e),a&512&&mu(e,e.return);break;case 3:if(oe(l,e),a&64&&(l=e.updateQueue,l!==null)){if(t=null,e.child!==null)switch(e.child.tag){case 27:case 5:t=e.child.stateNode;break;case 1:t=e.child.stateNode}try{Qs(l,t)}catch(i){bl(e,e.return,i)}}break;case 27:t===null&&a&4&&no(e);case 26:case 5:oe(l,e),t===null&&a&4&&ao(e),a&512&&mu(e,e.return);break;case 12:oe(l,e);break;case 13:oe(l,e),a&4&&ro(l,e),a&64&&(l=e.memoizedState,l!==null&&(l=l.dehydrated,l!==null&&(e=jh.bind(null,e),Ph(l,e))));break;case 22:if(a=e.memoizedState!==null||Kt,!a){t=t!==null&&t.memoizedState!==null||Rl,u=Kt;var n=Rl;Kt=a,(Rl=t)&&!n?de(l,e,(e.subtreeFlags&8772)!==0):oe(l,e),Kt=u,Rl=n}break;case 30:break;default:oe(l,e)}}function fo(l){var t=l.alternate;t!==null&&(l.alternate=null,fo(t)),l.child=null,l.deletions=null,l.sibling=null,l.tag===5&&(t=l.stateNode,t!==null&&ui(t)),l.stateNode=null,l.return=null,l.dependencies=null,l.memoizedProps=null,l.memoizedState=null,l.pendingProps=null,l.stateNode=null,l.updateQueue=null}var Tl=null,tt=!1;function kt(l,t,e){for(e=e.child;e!==null;)so(l,t,e),e=e.sibling}function so(l,t,e){if(ut&&typeof ut.onCommitFiberUnmount=="function")try{ut.onCommitFiberUnmount(ja,e)}catch{}switch(e.tag){case 26:Rl||Nt(e,t),kt(l,t,e),e.memoizedState?e.memoizedState.count--:e.stateNode&&(e=e.stateNode,e.parentNode.removeChild(e));break;case 27:Rl||Nt(e,t);var a=Tl,u=tt;be(e.type)&&(Tl=e.stateNode,tt=!1),kt(l,t,e),Tu(e.stateNode),Tl=a,tt=u;break;case 5:Rl||Nt(e,t);case 6:if(a=Tl,u=tt,Tl=null,kt(l,t,e),Tl=a,tt=u,Tl!==null)if(tt)try{(Tl.nodeType===9?Tl.body:Tl.nodeName==="HTML"?Tl.ownerDocument.body:Tl).removeChild(e.stateNode)}catch(n){bl(e,t,n)}else try{Tl.removeChild(e.stateNode)}catch(n){bl(e,t,n)}break;case 18:Tl!==null&&(tt?(l=Tl,Po(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.stateNode),Ru(l)):Po(Tl,e.stateNode));break;case 4:a=Tl,u=tt,Tl=e.stateNode.containerInfo,tt=!0,kt(l,t,e),Tl=a,tt=u;break;case 0:case 11:case 14:case 15:Rl||re(2,e,t),Rl||re(4,e,t),kt(l,t,e);break;case 1:Rl||(Nt(e,t),a=e.stateNode,typeof a.componentWillUnmount=="function"&&eo(e,t,a)),kt(l,t,e);break;case 21:kt(l,t,e);break;case 22:Rl=(a=Rl)||e.memoizedState!==null,kt(l,t,e),Rl=a;break;default:kt(l,t,e)}}function ro(l,t){if(t.memoizedState===null&&(l=t.alternate,l!==null&&(l=l.memoizedState,l!==null&&(l=l.dehydrated,l!==null))))try{Ru(l)}catch(e){bl(t,t.return,e)}}function _h(l){switch(l.tag){case 13:case 19:var t=l.stateNode;return t===null&&(t=l.stateNode=new io),t;case 22:return l=l.stateNode,t=l._retryCache,t===null&&(t=l._retryCache=new io),t;default:throw Error(s(435,l.tag))}}function _c(l,t){var e=_h(l);t.forEach(function(a){var u=Ch.bind(null,l,a);e.has(a)||(e.add(a),a.then(u,u))})}function ft(l,t){var e=t.deletions;if(e!==null)for(var a=0;a<e.length;a++){var u=e[a],n=l,i=t,c=i;l:for(;c!==null;){switch(c.tag){case 27:if(be(c.type)){Tl=c.stateNode,tt=!1;break l}break;case 5:Tl=c.stateNode,tt=!1;break l;case 3:case 4:Tl=c.stateNode.containerInfo,tt=!0;break l}c=c.return}if(Tl===null)throw Error(s(160));so(n,i,u),Tl=null,tt=!1,n=u.alternate,n!==null&&(n.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)oo(t,l),t=t.sibling}var zt=null;function oo(l,t){var e=l.alternate,a=l.flags;switch(l.tag){case 0:case 11:case 14:case 15:ft(t,l),st(l),a&4&&(re(3,l,l.return),du(3,l),re(5,l,l.return));break;case 1:ft(t,l),st(l),a&512&&(Rl||e===null||Nt(e,e.return)),a&64&&Kt&&(l=l.updateQueue,l!==null&&(a=l.callbacks,a!==null&&(e=l.shared.hiddenCallbacks,l.shared.hiddenCallbacks=e===null?a:e.concat(a))));break;case 26:var u=zt;if(ft(t,l),st(l),a&512&&(Rl||e===null||Nt(e,e.return)),a&4){var n=e!==null?e.memoizedState:null;if(a=l.memoizedState,e===null)if(a===null)if(l.stateNode===null){l:{a=l.type,e=l.memoizedProps,u=u.ownerDocument||u;t:switch(a){case"title":n=u.getElementsByTagName("title")[0],(!n||n[Ba]||n[Jl]||n.namespaceURI==="http://www.w3.org/2000/svg"||n.hasAttribute("itemprop"))&&(n=u.createElement(a),u.head.insertBefore(n,u.querySelector("head > title"))),kl(n,a,e),n[Jl]=l,Gl(n),a=n;break l;case"link":var i=id("link","href",u).get(a+(e.href||""));if(i){for(var c=0;c<i.length;c++)if(n=i[c],n.getAttribute("href")===(e.href==null||e.href===""?null:e.href)&&n.getAttribute("rel")===(e.rel==null?null:e.rel)&&n.getAttribute("title")===(e.title==null?null:e.title)&&n.getAttribute("crossorigin")===(e.crossOrigin==null?null:e.crossOrigin)){i.splice(c,1);break t}}n=u.createElement(a),kl(n,a,e),u.head.appendChild(n);break;case"meta":if(i=id("meta","content",u).get(a+(e.content||""))){for(c=0;c<i.length;c++)if(n=i[c],n.getAttribute("content")===(e.content==null?null:""+e.content)&&n.getAttribute("name")===(e.name==null?null:e.name)&&n.getAttribute("property")===(e.property==null?null:e.property)&&n.getAttribute("http-equiv")===(e.httpEquiv==null?null:e.httpEquiv)&&n.getAttribute("charset")===(e.charSet==null?null:e.charSet)){i.splice(c,1);break t}}n=u.createElement(a),kl(n,a,e),u.head.appendChild(n);break;default:throw Error(s(468,a))}n[Jl]=l,Gl(n),a=n}l.stateNode=a}else cd(u,l.type,l.stateNode);else l.stateNode=nd(u,a,l.memoizedProps);else n!==a?(n===null?e.stateNode!==null&&(e=e.stateNode,e.parentNode.removeChild(e)):n.count--,a===null?cd(u,l.type,l.stateNode):nd(u,a,l.memoizedProps)):a===null&&l.stateNode!==null&&Tc(l,l.memoizedProps,e.memoizedProps)}break;case 27:ft(t,l),st(l),a&512&&(Rl||e===null||Nt(e,e.return)),e!==null&&a&4&&Tc(l,l.memoizedProps,e.memoizedProps);break;case 5:if(ft(t,l),st(l),a&512&&(Rl||e===null||Nt(e,e.return)),l.flags&32){u=l.stateNode;try{Pe(u,"")}catch(b){bl(l,l.return,b)}}a&4&&l.stateNode!=null&&(u=l.memoizedProps,Tc(l,u,e!==null?e.memoizedProps:u)),a&1024&&(zc=!0);break;case 6:if(ft(t,l),st(l),a&4){if(l.stateNode===null)throw Error(s(162));a=l.memoizedProps,e=l.stateNode;try{e.nodeValue=a}catch(b){bl(l,l.return,b)}}break;case 3:if(wn=null,u=zt,zt=Yn(t.containerInfo),ft(t,l),zt=u,st(l),a&4&&e!==null&&e.memoizedState.isDehydrated)try{Ru(t.containerInfo)}catch(b){bl(l,l.return,b)}zc&&(zc=!1,mo(l));break;case 4:a=zt,zt=Yn(l.stateNode.containerInfo),ft(t,l),st(l),zt=a;break;case 12:ft(t,l),st(l);break;case 13:ft(t,l),st(l),l.child.flags&8192&&l.memoizedState!==null!=(e!==null&&e.memoizedState!==null)&&(Uc=Ot()),a&4&&(a=l.updateQueue,a!==null&&(l.updateQueue=null,_c(l,a)));break;case 22:u=l.memoizedState!==null;var r=e!==null&&e.memoizedState!==null,v=Kt,A=Rl;if(Kt=v||u,Rl=A||r,ft(t,l),Rl=A,Kt=v,st(l),a&8192)l:for(t=l.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(e===null||r||Kt||Rl||we(l)),e=null,t=l;;){if(t.tag===5||t.tag===26){if(e===null){r=e=t;try{if(n=r.stateNode,u)i=n.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{c=r.stateNode;var z=r.memoizedProps.style,g=z!=null&&z.hasOwnProperty("display")?z.display:null;c.style.display=g==null||typeof g=="boolean"?"":(""+g).trim()}}catch(b){bl(r,r.return,b)}}}else if(t.tag===6){if(e===null){r=t;try{r.stateNode.nodeValue=u?"":r.memoizedProps}catch(b){bl(r,r.return,b)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===l)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===l)break l;for(;t.sibling===null;){if(t.return===null||t.return===l)break l;e===t&&(e=null),t=t.return}e===t&&(e=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=l.updateQueue,a!==null&&(e=a.retryQueue,e!==null&&(a.retryQueue=null,_c(l,e))));break;case 19:ft(t,l),st(l),a&4&&(a=l.updateQueue,a!==null&&(l.updateQueue=null,_c(l,a)));break;case 30:break;case 21:break;default:ft(t,l),st(l)}}function st(l){var t=l.flags;if(t&2){try{for(var e,a=l.return;a!==null;){if(uo(a)){e=a;break}a=a.return}if(e==null)throw Error(s(160));switch(e.tag){case 27:var u=e.stateNode,n=Ac(l);zn(l,n,u);break;case 5:var i=e.stateNode;e.flags&32&&(Pe(i,""),e.flags&=-33);var c=Ac(l);zn(l,c,i);break;case 3:case 4:var r=e.stateNode.containerInfo,v=Ac(l);Ec(l,v,r);break;default:throw Error(s(161))}}catch(A){bl(l,l.return,A)}l.flags&=-3}t&4096&&(l.flags&=-4097)}function mo(l){if(l.subtreeFlags&1024)for(l=l.child;l!==null;){var t=l;mo(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),l=l.sibling}}function oe(l,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)co(l,t.alternate,t),t=t.sibling}function we(l){for(l=l.child;l!==null;){var t=l;switch(t.tag){case 0:case 11:case 14:case 15:re(4,t,t.return),we(t);break;case 1:Nt(t,t.return);var e=t.stateNode;typeof e.componentWillUnmount=="function"&&eo(t,t.return,e),we(t);break;case 27:Tu(t.stateNode);case 26:case 5:Nt(t,t.return),we(t);break;case 22:t.memoizedState===null&&we(t);break;case 30:we(t);break;default:we(t)}l=l.sibling}}function de(l,t,e){for(e=e&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,u=l,n=t,i=n.flags;switch(n.tag){case 0:case 11:case 15:de(u,n,e),du(4,n);break;case 1:if(de(u,n,e),a=n,u=a.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(v){bl(a,a.return,v)}if(a=n,u=a.updateQueue,u!==null){var c=a.stateNode;try{var r=u.shared.hiddenCallbacks;if(r!==null)for(u.shared.hiddenCallbacks=null,u=0;u<r.length;u++)Xs(r[u],c)}catch(v){bl(a,a.return,v)}}e&&i&64&&to(n),mu(n,n.return);break;case 27:no(n);case 26:case 5:de(u,n,e),e&&a===null&&i&4&&ao(n),mu(n,n.return);break;case 12:de(u,n,e);break;case 13:de(u,n,e),e&&i&4&&ro(u,n);break;case 22:n.memoizedState===null&&de(u,n,e),mu(n,n.return);break;case 30:break;default:de(u,n,e)}t=t.sibling}}function Mc(l,t){var e=null;l!==null&&l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(e=l.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==e&&(l!=null&&l.refCount++,e!=null&&Pa(e))}function Oc(l,t){l=null,t.alternate!==null&&(l=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==l&&(t.refCount++,l!=null&&Pa(l))}function Ut(l,t,e,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ho(l,t,e,a),t=t.sibling}function ho(l,t,e,a){var u=t.flags;switch(t.tag){case 0:case 11:case 15:Ut(l,t,e,a),u&2048&&du(9,t);break;case 1:Ut(l,t,e,a);break;case 3:Ut(l,t,e,a),u&2048&&(l=null,t.alternate!==null&&(l=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==l&&(t.refCount++,l!=null&&Pa(l)));break;case 12:if(u&2048){Ut(l,t,e,a),l=t.stateNode;try{var n=t.memoizedProps,i=n.id,c=n.onPostCommit;typeof c=="function"&&c(i,t.alternate===null?"mount":"update",l.passiveEffectDuration,-0)}catch(r){bl(t,t.return,r)}}else Ut(l,t,e,a);break;case 13:Ut(l,t,e,a);break;case 23:break;case 22:n=t.stateNode,i=t.alternate,t.memoizedState!==null?n._visibility&2?Ut(l,t,e,a):hu(l,t):n._visibility&2?Ut(l,t,e,a):(n._visibility|=2,ga(l,t,e,a,(t.subtreeFlags&10256)!==0)),u&2048&&Mc(i,t);break;case 24:Ut(l,t,e,a),u&2048&&Oc(t.alternate,t);break;default:Ut(l,t,e,a)}}function ga(l,t,e,a,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var n=l,i=t,c=e,r=a,v=i.flags;switch(i.tag){case 0:case 11:case 15:ga(n,i,c,r,u),du(8,i);break;case 23:break;case 22:var A=i.stateNode;i.memoizedState!==null?A._visibility&2?ga(n,i,c,r,u):hu(n,i):(A._visibility|=2,ga(n,i,c,r,u)),u&&v&2048&&Mc(i.alternate,i);break;case 24:ga(n,i,c,r,u),u&&v&2048&&Oc(i.alternate,i);break;default:ga(n,i,c,r,u)}t=t.sibling}}function hu(l,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var e=l,a=t,u=a.flags;switch(a.tag){case 22:hu(e,a),u&2048&&Mc(a.alternate,a);break;case 24:hu(e,a),u&2048&&Oc(a.alternate,a);break;default:hu(e,a)}t=t.sibling}}var vu=8192;function ba(l){if(l.subtreeFlags&vu)for(l=l.child;l!==null;)vo(l),l=l.sibling}function vo(l){switch(l.tag){case 26:ba(l),l.flags&vu&&l.memoizedState!==null&&o0(zt,l.memoizedState,l.memoizedProps);break;case 5:ba(l);break;case 3:case 4:var t=zt;zt=Yn(l.stateNode.containerInfo),ba(l),zt=t;break;case 22:l.memoizedState===null&&(t=l.alternate,t!==null&&t.memoizedState!==null?(t=vu,vu=16777216,ba(l),vu=t):ba(l));break;default:ba(l)}}function yo(l){var t=l.alternate;if(t!==null&&(l=t.child,l!==null)){t.child=null;do t=l.sibling,l.sibling=null,l=t;while(l!==null)}}function yu(l){var t=l.deletions;if((l.flags&16)!==0){if(t!==null)for(var e=0;e<t.length;e++){var a=t[e];Xl=a,bo(a,l)}yo(l)}if(l.subtreeFlags&10256)for(l=l.child;l!==null;)go(l),l=l.sibling}function go(l){switch(l.tag){case 0:case 11:case 15:yu(l),l.flags&2048&&re(9,l,l.return);break;case 3:yu(l);break;case 12:yu(l);break;case 22:var t=l.stateNode;l.memoizedState!==null&&t._visibility&2&&(l.return===null||l.return.tag!==13)?(t._visibility&=-3,_n(l)):yu(l);break;default:yu(l)}}function _n(l){var t=l.deletions;if((l.flags&16)!==0){if(t!==null)for(var e=0;e<t.length;e++){var a=t[e];Xl=a,bo(a,l)}yo(l)}for(l=l.child;l!==null;){switch(t=l,t.tag){case 0:case 11:case 15:re(8,t,t.return),_n(t);break;case 22:e=t.stateNode,e._visibility&2&&(e._visibility&=-3,_n(t));break;default:_n(t)}l=l.sibling}}function bo(l,t){for(;Xl!==null;){var e=Xl;switch(e.tag){case 0:case 11:case 15:re(8,e,t);break;case 23:case 22:if(e.memoizedState!==null&&e.memoizedState.cachePool!==null){var a=e.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Pa(e.memoizedState.cache)}if(a=e.child,a!==null)a.return=e,Xl=a;else l:for(e=l;Xl!==null;){a=Xl;var u=a.sibling,n=a.return;if(fo(a),a===e){Xl=null;break l}if(u!==null){u.return=n,Xl=u;break l}Xl=n}}}var Mh={getCacheForType:function(l){var t=Wl(Cl),e=t.data.get(l);return e===void 0&&(e=l(),t.data.set(l,e)),e}},Oh=typeof WeakMap=="function"?WeakMap:Map,ol=0,Sl=null,al=null,il=0,dl=0,rt=null,me=!1,pa=!1,Dc=!1,Jt=0,_l=0,he=0,Xe=0,Rc=0,xt=0,Sa=0,gu=null,et=null,Nc=!1,Uc=0,Mn=1/0,On=null,ve=null,Kl=0,ye=null,xa=null,Ta=0,Hc=0,jc=null,po=null,bu=0,Cc=null;function ot(){if((ol&2)!==0&&il!==0)return il&-il;if(x.T!==null){var l=sa;return l!==0?l:Qc()}return jf()}function So(){xt===0&&(xt=(il&536870912)===0||sl?Rf():536870912);var l=St.current;return l!==null&&(l.flags|=32),xt}function dt(l,t,e){(l===Sl&&(dl===2||dl===9)||l.cancelPendingCommit!==null)&&(Aa(l,0),ge(l,il,xt,!1)),qa(l,e),((ol&2)===0||l!==Sl)&&(l===Sl&&((ol&2)===0&&(Xe|=e),_l===4&&ge(l,il,xt,!1)),Ht(l))}function xo(l,t,e){if((ol&6)!==0)throw Error(s(327));var a=!e&&(t&124)===0&&(t&l.expiredLanes)===0||Ca(l,t),u=a?Nh(l,t):Yc(l,t,!0),n=a;do{if(u===0){pa&&!a&&ge(l,t,0,!1);break}else{if(e=l.current.alternate,n&&!Dh(e)){u=Yc(l,t,!1),n=!1;continue}if(u===2){if(n=t,l.errorRecoveryDisabledLanes&n)var i=0;else i=l.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){t=i;l:{var c=l;u=gu;var r=c.current.memoizedState.isDehydrated;if(r&&(Aa(c,i).flags|=256),i=Yc(c,i,!1),i!==2){if(Dc&&!r){c.errorRecoveryDisabledLanes|=n,Xe|=n,u=4;break l}n=et,et=u,n!==null&&(et===null?et=n:et.push.apply(et,n))}u=i}if(n=!1,u!==2)continue}}if(u===1){Aa(l,0),ge(l,t,0,!0);break}l:{switch(a=l,n=u,n){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:ge(a,t,xt,!me);break l;case 2:et=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(u=Uc+300-Ot(),10<u)){if(ge(a,t,xt,!me),Yu(a,0,!0)!==0)break l;a.timeoutHandle=$o(To.bind(null,a,e,et,On,Nc,t,xt,Xe,Sa,me,n,2,-0,0),u);break l}To(a,e,et,On,Nc,t,xt,Xe,Sa,me,n,0,-0,0)}}break}while(!0);Ht(l)}function To(l,t,e,a,u,n,i,c,r,v,A,z,g,b){if(l.timeoutHandle=-1,z=t.subtreeFlags,(z&8192||(z&16785408)===16785408)&&(zu={stylesheets:null,count:0,unsuspend:r0},vo(t),z=d0(),z!==null)){l.cancelPendingCommit=z(Do.bind(null,l,t,n,e,a,u,i,c,r,A,1,g,b)),ge(l,n,i,!v);return}Do(l,t,n,e,a,u,i,c,r)}function Dh(l){for(var t=l;;){var e=t.tag;if((e===0||e===11||e===15)&&t.flags&16384&&(e=t.updateQueue,e!==null&&(e=e.stores,e!==null)))for(var a=0;a<e.length;a++){var u=e[a],n=u.getSnapshot;u=u.value;try{if(!it(n(),u))return!1}catch{return!1}}if(e=t.child,t.subtreeFlags&16384&&e!==null)e.return=t,t=e;else{if(t===l)break;for(;t.sibling===null;){if(t.return===null||t.return===l)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ge(l,t,e,a){t&=~Rc,t&=~Xe,l.suspendedLanes|=t,l.pingedLanes&=~t,a&&(l.warmLanes|=t),a=l.expirationTimes;for(var u=t;0<u;){var n=31-nt(u),i=1<<n;a[n]=-1,u&=~i}e!==0&&Uf(l,e,t)}function Dn(){return(ol&6)===0?(pu(0),!1):!0}function qc(){if(al!==null){if(dl===0)var l=al.return;else l=al,wt=Ce=null,Ii(l),va=null,su=0,l=al;for(;l!==null;)lo(l.alternate,l),l=l.return;al=null}}function Aa(l,t){var e=l.timeoutHandle;e!==-1&&(l.timeoutHandle=-1,kh(e)),e=l.cancelPendingCommit,e!==null&&(l.cancelPendingCommit=null,e()),qc(),Sl=l,al=e=Bt(l.current,null),il=t,dl=0,rt=null,me=!1,pa=Ca(l,t),Dc=!1,Sa=xt=Rc=Xe=he=_l=0,et=gu=null,Nc=!1,(t&8)!==0&&(t|=t&32);var a=l.entangledLanes;if(a!==0)for(l=l.entanglements,a&=t;0<a;){var u=31-nt(a),n=1<<u;t|=l[u],a&=~n}return Jt=t,$u(),e}function Ao(l,t){I=null,x.H=yn,t===lu||t===nn?(t=Gs(),dl=3):t===qs?(t=Gs(),dl=4):dl=t===wr?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,rt=t,al===null&&(_l=1,xn(l,yt(t,l.current)))}function Eo(){var l=x.H;return x.H=yn,l===null?yn:l}function zo(){var l=x.A;return x.A=Mh,l}function Bc(){_l=4,me||(il&4194048)!==il&&St.current!==null||(pa=!0),(he&134217727)===0&&(Xe&134217727)===0||Sl===null||ge(Sl,il,xt,!1)}function Yc(l,t,e){var a=ol;ol|=2;var u=Eo(),n=zo();(Sl!==l||il!==t)&&(On=null,Aa(l,t)),t=!1;var i=_l;l:do try{if(dl!==0&&al!==null){var c=al,r=rt;switch(dl){case 8:qc(),i=6;break l;case 3:case 2:case 9:case 6:St.current===null&&(t=!0);var v=dl;if(dl=0,rt=null,Ea(l,c,r,v),e&&pa){i=0;break l}break;default:v=dl,dl=0,rt=null,Ea(l,c,r,v)}}Rh(),i=_l;break}catch(A){Ao(l,A)}while(!0);return t&&l.shellSuspendCounter++,wt=Ce=null,ol=a,x.H=u,x.A=n,al===null&&(Sl=null,il=0,$u()),i}function Rh(){for(;al!==null;)_o(al)}function Nh(l,t){var e=ol;ol|=2;var a=Eo(),u=zo();Sl!==l||il!==t?(On=null,Mn=Ot()+500,Aa(l,t)):pa=Ca(l,t);l:do try{if(dl!==0&&al!==null){t=al;var n=rt;t:switch(dl){case 1:dl=0,rt=null,Ea(l,t,n,1);break;case 2:case 9:if(Bs(n)){dl=0,rt=null,Mo(t);break}t=function(){dl!==2&&dl!==9||Sl!==l||(dl=7),Ht(l)},n.then(t,t);break l;case 3:dl=7;break l;case 4:dl=5;break l;case 7:Bs(n)?(dl=0,rt=null,Mo(t)):(dl=0,rt=null,Ea(l,t,n,7));break;case 5:var i=null;switch(al.tag){case 26:i=al.memoizedState;case 5:case 27:var c=al;if(!i||fd(i)){dl=0,rt=null;var r=c.sibling;if(r!==null)al=r;else{var v=c.return;v!==null?(al=v,Rn(v)):al=null}break t}}dl=0,rt=null,Ea(l,t,n,5);break;case 6:dl=0,rt=null,Ea(l,t,n,6);break;case 8:qc(),_l=6;break l;default:throw Error(s(462))}}Uh();break}catch(A){Ao(l,A)}while(!0);return wt=Ce=null,x.H=a,x.A=u,ol=e,al!==null?0:(Sl=null,il=0,$u(),_l)}function Uh(){for(;al!==null&&!lm();)_o(al)}function _o(l){var t=Pr(l.alternate,l,Jt);l.memoizedProps=l.pendingProps,t===null?Rn(l):al=t}function Mo(l){var t=l,e=t.alternate;switch(t.tag){case 15:case 0:t=Kr(e,t,t.pendingProps,t.type,void 0,il);break;case 11:t=Kr(e,t,t.pendingProps,t.type.render,t.ref,il);break;case 5:Ii(t);default:lo(e,t),t=al=Ms(t,Jt),t=Pr(e,t,Jt)}l.memoizedProps=l.pendingProps,t===null?Rn(l):al=t}function Ea(l,t,e,a){wt=Ce=null,Ii(t),va=null,su=0;var u=t.return;try{if(xh(l,u,t,e,il)){_l=1,xn(l,yt(e,l.current)),al=null;return}}catch(n){if(u!==null)throw al=u,n;_l=1,xn(l,yt(e,l.current)),al=null;return}t.flags&32768?(sl||a===1?l=!0:pa||(il&536870912)!==0?l=!1:(me=l=!0,(a===2||a===9||a===3||a===6)&&(a=St.current,a!==null&&a.tag===13&&(a.flags|=16384))),Oo(t,l)):Rn(t)}function Rn(l){var t=l;do{if((t.flags&32768)!==0){Oo(t,me);return}l=t.return;var e=Ah(t.alternate,t,Jt);if(e!==null){al=e;return}if(t=t.sibling,t!==null){al=t;return}al=t=l}while(t!==null);_l===0&&(_l=5)}function Oo(l,t){do{var e=Eh(l.alternate,l);if(e!==null){e.flags&=32767,al=e;return}if(e=l.return,e!==null&&(e.flags|=32768,e.subtreeFlags=0,e.deletions=null),!t&&(l=l.sibling,l!==null)){al=l;return}al=l=e}while(l!==null);_l=6,al=null}function Do(l,t,e,a,u,n,i,c,r){l.cancelPendingCommit=null;do Nn();while(Kl!==0);if((ol&6)!==0)throw Error(s(327));if(t!==null){if(t===l.current)throw Error(s(177));if(n=t.lanes|t.childLanes,n|=Oi,rm(l,e,n,i,c,r),l===Sl&&(al=Sl=null,il=0),xa=t,ye=l,Ta=e,Hc=n,jc=u,po=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(l.callbackNode=null,l.callbackPriority=0,qh(Cu,function(){return jo(),null})):(l.callbackNode=null,l.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=x.T,x.T=null,u=H.p,H.p=2,i=ol,ol|=4;try{zh(l,t,e)}finally{ol=i,H.p=u,x.T=a}}Kl=1,Ro(),No(),Uo()}}function Ro(){if(Kl===1){Kl=0;var l=ye,t=xa,e=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||e){e=x.T,x.T=null;var a=H.p;H.p=2;var u=ol;ol|=4;try{oo(t,l);var n=$c,i=gs(l.containerInfo),c=n.focusedElem,r=n.selectionRange;if(i!==c&&c&&c.ownerDocument&&ys(c.ownerDocument.documentElement,c)){if(r!==null&&Ai(c)){var v=r.start,A=r.end;if(A===void 0&&(A=v),"selectionStart"in c)c.selectionStart=v,c.selectionEnd=Math.min(A,c.value.length);else{var z=c.ownerDocument||document,g=z&&z.defaultView||window;if(g.getSelection){var b=g.getSelection(),K=c.textContent.length,V=Math.min(r.start,K),yl=r.end===void 0?V:Math.min(r.end,K);!b.extend&&V>yl&&(i=yl,yl=V,V=i);var m=vs(c,V),d=vs(c,yl);if(m&&d&&(b.rangeCount!==1||b.anchorNode!==m.node||b.anchorOffset!==m.offset||b.focusNode!==d.node||b.focusOffset!==d.offset)){var h=z.createRange();h.setStart(m.node,m.offset),b.removeAllRanges(),V>yl?(b.addRange(h),b.extend(d.node,d.offset)):(h.setEnd(d.node,d.offset),b.addRange(h))}}}}for(z=[],b=c;b=b.parentNode;)b.nodeType===1&&z.push({element:b,left:b.scrollLeft,top:b.scrollTop});for(typeof c.focus=="function"&&c.focus(),c=0;c<z.length;c++){var E=z[c];E.element.scrollLeft=E.left,E.element.scrollTop=E.top}}Zn=!!Wc,$c=Wc=null}finally{ol=u,H.p=a,x.T=e}}l.current=t,Kl=2}}function No(){if(Kl===2){Kl=0;var l=ye,t=xa,e=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||e){e=x.T,x.T=null;var a=H.p;H.p=2;var u=ol;ol|=4;try{co(l,t.alternate,t)}finally{ol=u,H.p=a,x.T=e}}Kl=3}}function Uo(){if(Kl===4||Kl===3){Kl=0,tm();var l=ye,t=xa,e=Ta,a=po;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Kl=5:(Kl=0,xa=ye=null,Ho(l,l.pendingLanes));var u=l.pendingLanes;if(u===0&&(ve=null),ei(e),t=t.stateNode,ut&&typeof ut.onCommitFiberRoot=="function")try{ut.onCommitFiberRoot(ja,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=x.T,u=H.p,H.p=2,x.T=null;try{for(var n=l.onRecoverableError,i=0;i<a.length;i++){var c=a[i];n(c.value,{componentStack:c.stack})}}finally{x.T=t,H.p=u}}(Ta&3)!==0&&Nn(),Ht(l),u=l.pendingLanes,(e&4194090)!==0&&(u&42)!==0?l===Cc?bu++:(bu=0,Cc=l):bu=0,pu(0)}}function Ho(l,t){(l.pooledCacheLanes&=t)===0&&(t=l.pooledCache,t!=null&&(l.pooledCache=null,Pa(t)))}function Nn(l){return Ro(),No(),Uo(),jo()}function jo(){if(Kl!==5)return!1;var l=ye,t=Hc;Hc=0;var e=ei(Ta),a=x.T,u=H.p;try{H.p=32>e?32:e,x.T=null,e=jc,jc=null;var n=ye,i=Ta;if(Kl=0,xa=ye=null,Ta=0,(ol&6)!==0)throw Error(s(331));var c=ol;if(ol|=4,go(n.current),ho(n,n.current,i,e),ol=c,pu(0,!1),ut&&typeof ut.onPostCommitFiberRoot=="function")try{ut.onPostCommitFiberRoot(ja,n)}catch{}return!0}finally{H.p=u,x.T=a,Ho(l,t)}}function Co(l,t,e){t=yt(e,t),t=mc(l.stateNode,t,2),l=ie(l,t,2),l!==null&&(qa(l,2),Ht(l))}function bl(l,t,e){if(l.tag===3)Co(l,l,e);else for(;t!==null;){if(t.tag===3){Co(t,l,e);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(ve===null||!ve.has(a))){l=yt(e,l),e=Yr(2),a=ie(t,e,2),a!==null&&(Gr(e,a,t,l),qa(a,2),Ht(a));break}}t=t.return}}function Gc(l,t,e){var a=l.pingCache;if(a===null){a=l.pingCache=new Oh;var u=new Set;a.set(t,u)}else u=a.get(t),u===void 0&&(u=new Set,a.set(t,u));u.has(e)||(Dc=!0,u.add(e),l=Hh.bind(null,l,t,e),t.then(l,l))}function Hh(l,t,e){var a=l.pingCache;a!==null&&a.delete(t),l.pingedLanes|=l.suspendedLanes&e,l.warmLanes&=~e,Sl===l&&(il&e)===e&&(_l===4||_l===3&&(il&62914560)===il&&300>Ot()-Uc?(ol&2)===0&&Aa(l,0):Rc|=e,Sa===il&&(Sa=0)),Ht(l)}function qo(l,t){t===0&&(t=Nf()),l=na(l,t),l!==null&&(qa(l,t),Ht(l))}function jh(l){var t=l.memoizedState,e=0;t!==null&&(e=t.retryLane),qo(l,e)}function Ch(l,t){var e=0;switch(l.tag){case 13:var a=l.stateNode,u=l.memoizedState;u!==null&&(e=u.retryLane);break;case 19:a=l.stateNode;break;case 22:a=l.stateNode._retryCache;break;default:throw Error(s(314))}a!==null&&a.delete(t),qo(l,e)}function qh(l,t){return Pn(l,t)}var Un=null,za=null,wc=!1,Hn=!1,Xc=!1,Qe=0;function Ht(l){l!==za&&l.next===null&&(za===null?Un=za=l:za=za.next=l),Hn=!0,wc||(wc=!0,Yh())}function pu(l,t){if(!Xc&&Hn){Xc=!0;do for(var e=!1,a=Un;a!==null;){if(l!==0){var u=a.pendingLanes;if(u===0)var n=0;else{var i=a.suspendedLanes,c=a.pingedLanes;n=(1<<31-nt(42|l)+1)-1,n&=u&~(i&~c),n=n&201326741?n&201326741|1:n?n|2:0}n!==0&&(e=!0,wo(a,n))}else n=il,n=Yu(a,a===Sl?n:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(n&3)===0||Ca(a,n)||(e=!0,wo(a,n));a=a.next}while(e);Xc=!1}}function Bh(){Bo()}function Bo(){Hn=wc=!1;var l=0;Qe!==0&&(Kh()&&(l=Qe),Qe=0);for(var t=Ot(),e=null,a=Un;a!==null;){var u=a.next,n=Yo(a,t);n===0?(a.next=null,e===null?Un=u:e.next=u,u===null&&(za=e)):(e=a,(l!==0||(n&3)!==0)&&(Hn=!0)),a=u}pu(l)}function Yo(l,t){for(var e=l.suspendedLanes,a=l.pingedLanes,u=l.expirationTimes,n=l.pendingLanes&-62914561;0<n;){var i=31-nt(n),c=1<<i,r=u[i];r===-1?((c&e)===0||(c&a)!==0)&&(u[i]=sm(c,t)):r<=t&&(l.expiredLanes|=c),n&=~c}if(t=Sl,e=il,e=Yu(l,l===t?e:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),a=l.callbackNode,e===0||l===t&&(dl===2||dl===9)||l.cancelPendingCommit!==null)return a!==null&&a!==null&&In(a),l.callbackNode=null,l.callbackPriority=0;if((e&3)===0||Ca(l,e)){if(t=e&-e,t===l.callbackPriority)return t;switch(a!==null&&In(a),ei(e)){case 2:case 8:e=Of;break;case 32:e=Cu;break;case 268435456:e=Df;break;default:e=Cu}return a=Go.bind(null,l),e=Pn(e,a),l.callbackPriority=t,l.callbackNode=e,t}return a!==null&&a!==null&&In(a),l.callbackPriority=2,l.callbackNode=null,2}function Go(l,t){if(Kl!==0&&Kl!==5)return l.callbackNode=null,l.callbackPriority=0,null;var e=l.callbackNode;if(Nn()&&l.callbackNode!==e)return null;var a=il;return a=Yu(l,l===Sl?a:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),a===0?null:(xo(l,a,t),Yo(l,Ot()),l.callbackNode!=null&&l.callbackNode===e?Go.bind(null,l):null)}function wo(l,t){if(Nn())return null;xo(l,t,!0)}function Yh(){Jh(function(){(ol&6)!==0?Pn(Mf,Bh):Bo()})}function Qc(){return Qe===0&&(Qe=Rf()),Qe}function Xo(l){return l==null||typeof l=="symbol"||typeof l=="boolean"?null:typeof l=="function"?l:Zu(""+l)}function Qo(l,t){var e=t.ownerDocument.createElement("input");return e.name=t.name,e.value=t.value,l.id&&e.setAttribute("form",l.id),t.parentNode.insertBefore(e,t),l=new FormData(l),e.parentNode.removeChild(e),l}function Gh(l,t,e,a,u){if(t==="submit"&&e&&e.stateNode===u){var n=Xo((u[Pl]||null).action),i=a.submitter;i&&(t=(t=i[Pl]||null)?Xo(t.formAction):i.getAttribute("formAction"),t!==null&&(n=t,i=null));var c=new ku("action","action",null,a,u);l.push({event:c,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Qe!==0){var r=i?Qo(u,i):new FormData(u);fc(e,{pending:!0,data:r,method:u.method,action:n},null,r)}}else typeof n=="function"&&(c.preventDefault(),r=i?Qo(u,i):new FormData(u),fc(e,{pending:!0,data:r,method:u.method,action:n},n,r))},currentTarget:u}]})}}for(var Zc=0;Zc<Mi.length;Zc++){var Vc=Mi[Zc],wh=Vc.toLowerCase(),Xh=Vc[0].toUpperCase()+Vc.slice(1);Et(wh,"on"+Xh)}Et(Ss,"onAnimationEnd"),Et(xs,"onAnimationIteration"),Et(Ts,"onAnimationStart"),Et("dblclick","onDoubleClick"),Et("focusin","onFocus"),Et("focusout","onBlur"),Et(uh,"onTransitionRun"),Et(nh,"onTransitionStart"),Et(ih,"onTransitionCancel"),Et(As,"onTransitionEnd"),We("onMouseEnter",["mouseout","mouseover"]),We("onMouseLeave",["mouseout","mouseover"]),We("onPointerEnter",["pointerout","pointerover"]),We("onPointerLeave",["pointerout","pointerover"]),_e("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),_e("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),_e("onBeforeInput",["compositionend","keypress","textInput","paste"]),_e("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),_e("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),_e("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Su="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Qh=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Su));function Zo(l,t){t=(t&4)!==0;for(var e=0;e<l.length;e++){var a=l[e],u=a.event;a=a.listeners;l:{var n=void 0;if(t)for(var i=a.length-1;0<=i;i--){var c=a[i],r=c.instance,v=c.currentTarget;if(c=c.listener,r!==n&&u.isPropagationStopped())break l;n=c,u.currentTarget=v;try{n(u)}catch(A){Sn(A)}u.currentTarget=null,n=r}else for(i=0;i<a.length;i++){if(c=a[i],r=c.instance,v=c.currentTarget,c=c.listener,r!==n&&u.isPropagationStopped())break l;n=c,u.currentTarget=v;try{n(u)}catch(A){Sn(A)}u.currentTarget=null,n=r}}}}function ul(l,t){var e=t[ai];e===void 0&&(e=t[ai]=new Set);var a=l+"__bubble";e.has(a)||(Vo(t,l,2,!1),e.add(a))}function Lc(l,t,e){var a=0;t&&(a|=4),Vo(e,l,a,t)}var jn="_reactListening"+Math.random().toString(36).slice(2);function Kc(l){if(!l[jn]){l[jn]=!0,qf.forEach(function(e){e!=="selectionchange"&&(Qh.has(e)||Lc(e,!1,l),Lc(e,!0,l))});var t=l.nodeType===9?l:l.ownerDocument;t===null||t[jn]||(t[jn]=!0,Lc("selectionchange",!1,t))}}function Vo(l,t,e,a){switch(hd(t)){case 2:var u=v0;break;case 8:u=y0;break;default:u=cf}e=u.bind(null,t,e,l),u=void 0,!hi||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),a?u!==void 0?l.addEventListener(t,e,{capture:!0,passive:u}):l.addEventListener(t,e,!0):u!==void 0?l.addEventListener(t,e,{passive:u}):l.addEventListener(t,e,!1)}function kc(l,t,e,a,u){var n=a;if((t&1)===0&&(t&2)===0&&a!==null)l:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var c=a.stateNode.containerInfo;if(c===u)break;if(i===4)for(i=a.return;i!==null;){var r=i.tag;if((r===3||r===4)&&i.stateNode.containerInfo===u)return;i=i.return}for(;c!==null;){if(i=Ke(c),i===null)return;if(r=i.tag,r===5||r===6||r===26||r===27){a=n=i;continue l}c=c.parentNode}}a=a.return}$f(function(){var v=n,A=di(e),z=[];l:{var g=Es.get(l);if(g!==void 0){var b=ku,K=l;switch(l){case"keypress":if(Lu(e)===0)break l;case"keydown":case"keyup":b=qm;break;case"focusin":K="focus",b=bi;break;case"focusout":K="blur",b=bi;break;case"beforeblur":case"afterblur":b=bi;break;case"click":if(e.button===2)break l;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":b=If;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":b=Em;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":b=Gm;break;case Ss:case xs:case Ts:b=Mm;break;case As:b=Xm;break;case"scroll":case"scrollend":b=Tm;break;case"wheel":b=Zm;break;case"copy":case"cut":case"paste":b=Dm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":b=ts;break;case"toggle":case"beforetoggle":b=Lm}var V=(t&4)!==0,yl=!V&&(l==="scroll"||l==="scrollend"),m=V?g!==null?g+"Capture":null:g;V=[];for(var d=v,h;d!==null;){var E=d;if(h=E.stateNode,E=E.tag,E!==5&&E!==26&&E!==27||h===null||m===null||(E=Ga(d,m),E!=null&&V.push(xu(d,E,h))),yl)break;d=d.return}0<V.length&&(g=new b(g,K,null,e,A),z.push({event:g,listeners:V}))}}if((t&7)===0){l:{if(g=l==="mouseover"||l==="pointerover",b=l==="mouseout"||l==="pointerout",g&&e!==oi&&(K=e.relatedTarget||e.fromElement)&&(Ke(K)||K[Le]))break l;if((b||g)&&(g=A.window===A?A:(g=A.ownerDocument)?g.defaultView||g.parentWindow:window,b?(K=e.relatedTarget||e.toElement,b=v,K=K?Ke(K):null,K!==null&&(yl=O(K),V=K.tag,K!==yl||V!==5&&V!==27&&V!==6)&&(K=null)):(b=null,K=v),b!==K)){if(V=If,E="onMouseLeave",m="onMouseEnter",d="mouse",(l==="pointerout"||l==="pointerover")&&(V=ts,E="onPointerLeave",m="onPointerEnter",d="pointer"),yl=b==null?g:Ya(b),h=K==null?g:Ya(K),g=new V(E,d+"leave",b,e,A),g.target=yl,g.relatedTarget=h,E=null,Ke(A)===v&&(V=new V(m,d+"enter",K,e,A),V.target=h,V.relatedTarget=yl,E=V),yl=E,b&&K)t:{for(V=b,m=K,d=0,h=V;h;h=_a(h))d++;for(h=0,E=m;E;E=_a(E))h++;for(;0<d-h;)V=_a(V),d--;for(;0<h-d;)m=_a(m),h--;for(;d--;){if(V===m||m!==null&&V===m.alternate)break t;V=_a(V),m=_a(m)}V=null}else V=null;b!==null&&Lo(z,g,b,V,!1),K!==null&&yl!==null&&Lo(z,yl,K,V,!0)}}l:{if(g=v?Ya(v):window,b=g.nodeName&&g.nodeName.toLowerCase(),b==="select"||b==="input"&&g.type==="file")var B=ss;else if(cs(g))if(rs)B=th;else{B=Im;var tl=Pm}else b=g.nodeName,!b||b.toLowerCase()!=="input"||g.type!=="checkbox"&&g.type!=="radio"?v&&ri(v.elementType)&&(B=ss):B=lh;if(B&&(B=B(l,v))){fs(z,B,e,A);break l}tl&&tl(l,g,v),l==="focusout"&&v&&g.type==="number"&&v.memoizedProps.value!=null&&si(g,"number",g.value)}switch(tl=v?Ya(v):window,l){case"focusin":(cs(tl)||tl.contentEditable==="true")&&(ea=tl,Ei=v,ka=null);break;case"focusout":ka=Ei=ea=null;break;case"mousedown":zi=!0;break;case"contextmenu":case"mouseup":case"dragend":zi=!1,bs(z,e,A);break;case"selectionchange":if(ah)break;case"keydown":case"keyup":bs(z,e,A)}var X;if(Si)l:{switch(l){case"compositionstart":var L="onCompositionStart";break l;case"compositionend":L="onCompositionEnd";break l;case"compositionupdate":L="onCompositionUpdate";break l}L=void 0}else ta?ns(l,e)&&(L="onCompositionEnd"):l==="keydown"&&e.keyCode===229&&(L="onCompositionStart");L&&(es&&e.locale!=="ko"&&(ta||L!=="onCompositionStart"?L==="onCompositionEnd"&&ta&&(X=Ff()):(ee=A,vi="value"in ee?ee.value:ee.textContent,ta=!0)),tl=Cn(v,L),0<tl.length&&(L=new ls(L,l,null,e,A),z.push({event:L,listeners:tl}),X?L.data=X:(X=is(e),X!==null&&(L.data=X)))),(X=km?Jm(l,e):Wm(l,e))&&(L=Cn(v,"onBeforeInput"),0<L.length&&(tl=new ls("onBeforeInput","beforeinput",null,e,A),z.push({event:tl,listeners:L}),tl.data=X)),Gh(z,l,v,e,A)}Zo(z,t)})}function xu(l,t,e){return{instance:l,listener:t,currentTarget:e}}function Cn(l,t){for(var e=t+"Capture",a=[];l!==null;){var u=l,n=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||n===null||(u=Ga(l,e),u!=null&&a.unshift(xu(l,u,n)),u=Ga(l,t),u!=null&&a.push(xu(l,u,n))),l.tag===3)return a;l=l.return}return[]}function _a(l){if(l===null)return null;do l=l.return;while(l&&l.tag!==5&&l.tag!==27);return l||null}function Lo(l,t,e,a,u){for(var n=t._reactName,i=[];e!==null&&e!==a;){var c=e,r=c.alternate,v=c.stateNode;if(c=c.tag,r!==null&&r===a)break;c!==5&&c!==26&&c!==27||v===null||(r=v,u?(v=Ga(e,n),v!=null&&i.unshift(xu(e,v,r))):u||(v=Ga(e,n),v!=null&&i.push(xu(e,v,r)))),e=e.return}i.length!==0&&l.push({event:t,listeners:i})}var Zh=/\r\n?/g,Vh=/\u0000|\uFFFD/g;function Ko(l){return(typeof l=="string"?l:""+l).replace(Zh,`
`).replace(Vh,"")}function ko(l,t){return t=Ko(t),Ko(l)===t}function qn(){}function vl(l,t,e,a,u,n){switch(e){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||Pe(l,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&Pe(l,""+a);break;case"className":wu(l,"class",a);break;case"tabIndex":wu(l,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":wu(l,e,a);break;case"style":Jf(l,a,n);break;case"data":if(t!=="object"){wu(l,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||e!=="href")){l.removeAttribute(e);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){l.removeAttribute(e);break}a=Zu(""+a),l.setAttribute(e,a);break;case"action":case"formAction":if(typeof a=="function"){l.setAttribute(e,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof n=="function"&&(e==="formAction"?(t!=="input"&&vl(l,t,"name",u.name,u,null),vl(l,t,"formEncType",u.formEncType,u,null),vl(l,t,"formMethod",u.formMethod,u,null),vl(l,t,"formTarget",u.formTarget,u,null)):(vl(l,t,"encType",u.encType,u,null),vl(l,t,"method",u.method,u,null),vl(l,t,"target",u.target,u,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){l.removeAttribute(e);break}a=Zu(""+a),l.setAttribute(e,a);break;case"onClick":a!=null&&(l.onclick=qn);break;case"onScroll":a!=null&&ul("scroll",l);break;case"onScrollEnd":a!=null&&ul("scrollend",l);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(e=a.__html,e!=null){if(u.children!=null)throw Error(s(60));l.innerHTML=e}}break;case"multiple":l.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":l.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){l.removeAttribute("xlink:href");break}e=Zu(""+a),l.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",e);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?l.setAttribute(e,""+a):l.removeAttribute(e);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?l.setAttribute(e,""):l.removeAttribute(e);break;case"capture":case"download":a===!0?l.setAttribute(e,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?l.setAttribute(e,a):l.removeAttribute(e);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?l.setAttribute(e,a):l.removeAttribute(e);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?l.removeAttribute(e):l.setAttribute(e,a);break;case"popover":ul("beforetoggle",l),ul("toggle",l),Gu(l,"popover",a);break;case"xlinkActuate":Ct(l,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Ct(l,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Ct(l,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Ct(l,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Ct(l,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Ct(l,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Ct(l,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Ct(l,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Ct(l,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Gu(l,"is",a);break;case"innerText":case"textContent":break;default:(!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(e=Sm.get(e)||e,Gu(l,e,a))}}function Jc(l,t,e,a,u,n){switch(e){case"style":Jf(l,a,n);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(e=a.__html,e!=null){if(u.children!=null)throw Error(s(60));l.innerHTML=e}}break;case"children":typeof a=="string"?Pe(l,a):(typeof a=="number"||typeof a=="bigint")&&Pe(l,""+a);break;case"onScroll":a!=null&&ul("scroll",l);break;case"onScrollEnd":a!=null&&ul("scrollend",l);break;case"onClick":a!=null&&(l.onclick=qn);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Bf.hasOwnProperty(e))l:{if(e[0]==="o"&&e[1]==="n"&&(u=e.endsWith("Capture"),t=e.slice(2,u?e.length-7:void 0),n=l[Pl]||null,n=n!=null?n[e]:null,typeof n=="function"&&l.removeEventListener(t,n,u),typeof a=="function")){typeof n!="function"&&n!==null&&(e in l?l[e]=null:l.hasAttribute(e)&&l.removeAttribute(e)),l.addEventListener(t,a,u);break l}e in l?l[e]=a:a===!0?l.setAttribute(e,""):Gu(l,e,a)}}}function kl(l,t,e){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ul("error",l),ul("load",l);var a=!1,u=!1,n;for(n in e)if(e.hasOwnProperty(n)){var i=e[n];if(i!=null)switch(n){case"src":a=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:vl(l,t,n,i,e,null)}}u&&vl(l,t,"srcSet",e.srcSet,e,null),a&&vl(l,t,"src",e.src,e,null);return;case"input":ul("invalid",l);var c=n=i=u=null,r=null,v=null;for(a in e)if(e.hasOwnProperty(a)){var A=e[a];if(A!=null)switch(a){case"name":u=A;break;case"type":i=A;break;case"checked":r=A;break;case"defaultChecked":v=A;break;case"value":n=A;break;case"defaultValue":c=A;break;case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(s(137,t));break;default:vl(l,t,a,A,e,null)}}Vf(l,n,c,r,v,i,u,!1),Xu(l);return;case"select":ul("invalid",l),a=i=n=null;for(u in e)if(e.hasOwnProperty(u)&&(c=e[u],c!=null))switch(u){case"value":n=c;break;case"defaultValue":i=c;break;case"multiple":a=c;default:vl(l,t,u,c,e,null)}t=n,e=i,l.multiple=!!a,t!=null?Fe(l,!!a,t,!1):e!=null&&Fe(l,!!a,e,!0);return;case"textarea":ul("invalid",l),n=u=a=null;for(i in e)if(e.hasOwnProperty(i)&&(c=e[i],c!=null))switch(i){case"value":a=c;break;case"defaultValue":u=c;break;case"children":n=c;break;case"dangerouslySetInnerHTML":if(c!=null)throw Error(s(91));break;default:vl(l,t,i,c,e,null)}Kf(l,a,u,n),Xu(l);return;case"option":for(r in e)if(e.hasOwnProperty(r)&&(a=e[r],a!=null))switch(r){case"selected":l.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:vl(l,t,r,a,e,null)}return;case"dialog":ul("beforetoggle",l),ul("toggle",l),ul("cancel",l),ul("close",l);break;case"iframe":case"object":ul("load",l);break;case"video":case"audio":for(a=0;a<Su.length;a++)ul(Su[a],l);break;case"image":ul("error",l),ul("load",l);break;case"details":ul("toggle",l);break;case"embed":case"source":case"link":ul("error",l),ul("load",l);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(v in e)if(e.hasOwnProperty(v)&&(a=e[v],a!=null))switch(v){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:vl(l,t,v,a,e,null)}return;default:if(ri(t)){for(A in e)e.hasOwnProperty(A)&&(a=e[A],a!==void 0&&Jc(l,t,A,a,e,void 0));return}}for(c in e)e.hasOwnProperty(c)&&(a=e[c],a!=null&&vl(l,t,c,a,e,null))}function Lh(l,t,e,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,n=null,i=null,c=null,r=null,v=null,A=null;for(b in e){var z=e[b];if(e.hasOwnProperty(b)&&z!=null)switch(b){case"checked":break;case"value":break;case"defaultValue":r=z;default:a.hasOwnProperty(b)||vl(l,t,b,null,a,z)}}for(var g in a){var b=a[g];if(z=e[g],a.hasOwnProperty(g)&&(b!=null||z!=null))switch(g){case"type":n=b;break;case"name":u=b;break;case"checked":v=b;break;case"defaultChecked":A=b;break;case"value":i=b;break;case"defaultValue":c=b;break;case"children":case"dangerouslySetInnerHTML":if(b!=null)throw Error(s(137,t));break;default:b!==z&&vl(l,t,g,b,a,z)}}fi(l,i,c,r,v,A,n,u);return;case"select":b=i=c=g=null;for(n in e)if(r=e[n],e.hasOwnProperty(n)&&r!=null)switch(n){case"value":break;case"multiple":b=r;default:a.hasOwnProperty(n)||vl(l,t,n,null,a,r)}for(u in a)if(n=a[u],r=e[u],a.hasOwnProperty(u)&&(n!=null||r!=null))switch(u){case"value":g=n;break;case"defaultValue":c=n;break;case"multiple":i=n;default:n!==r&&vl(l,t,u,n,a,r)}t=c,e=i,a=b,g!=null?Fe(l,!!e,g,!1):!!a!=!!e&&(t!=null?Fe(l,!!e,t,!0):Fe(l,!!e,e?[]:"",!1));return;case"textarea":b=g=null;for(c in e)if(u=e[c],e.hasOwnProperty(c)&&u!=null&&!a.hasOwnProperty(c))switch(c){case"value":break;case"children":break;default:vl(l,t,c,null,a,u)}for(i in a)if(u=a[i],n=e[i],a.hasOwnProperty(i)&&(u!=null||n!=null))switch(i){case"value":g=u;break;case"defaultValue":b=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(s(91));break;default:u!==n&&vl(l,t,i,u,a,n)}Lf(l,g,b);return;case"option":for(var K in e)if(g=e[K],e.hasOwnProperty(K)&&g!=null&&!a.hasOwnProperty(K))switch(K){case"selected":l.selected=!1;break;default:vl(l,t,K,null,a,g)}for(r in a)if(g=a[r],b=e[r],a.hasOwnProperty(r)&&g!==b&&(g!=null||b!=null))switch(r){case"selected":l.selected=g&&typeof g!="function"&&typeof g!="symbol";break;default:vl(l,t,r,g,a,b)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var V in e)g=e[V],e.hasOwnProperty(V)&&g!=null&&!a.hasOwnProperty(V)&&vl(l,t,V,null,a,g);for(v in a)if(g=a[v],b=e[v],a.hasOwnProperty(v)&&g!==b&&(g!=null||b!=null))switch(v){case"children":case"dangerouslySetInnerHTML":if(g!=null)throw Error(s(137,t));break;default:vl(l,t,v,g,a,b)}return;default:if(ri(t)){for(var yl in e)g=e[yl],e.hasOwnProperty(yl)&&g!==void 0&&!a.hasOwnProperty(yl)&&Jc(l,t,yl,void 0,a,g);for(A in a)g=a[A],b=e[A],!a.hasOwnProperty(A)||g===b||g===void 0&&b===void 0||Jc(l,t,A,g,a,b);return}}for(var m in e)g=e[m],e.hasOwnProperty(m)&&g!=null&&!a.hasOwnProperty(m)&&vl(l,t,m,null,a,g);for(z in a)g=a[z],b=e[z],!a.hasOwnProperty(z)||g===b||g==null&&b==null||vl(l,t,z,g,a,b)}var Wc=null,$c=null;function Bn(l){return l.nodeType===9?l:l.ownerDocument}function Jo(l){switch(l){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Wo(l,t){if(l===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return l===1&&t==="foreignObject"?0:l}function Fc(l,t){return l==="textarea"||l==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Pc=null;function Kh(){var l=window.event;return l&&l.type==="popstate"?l===Pc?!1:(Pc=l,!0):(Pc=null,!1)}var $o=typeof setTimeout=="function"?setTimeout:void 0,kh=typeof clearTimeout=="function"?clearTimeout:void 0,Fo=typeof Promise=="function"?Promise:void 0,Jh=typeof queueMicrotask=="function"?queueMicrotask:typeof Fo<"u"?function(l){return Fo.resolve(null).then(l).catch(Wh)}:$o;function Wh(l){setTimeout(function(){throw l})}function be(l){return l==="head"}function Po(l,t){var e=t,a=0,u=0;do{var n=e.nextSibling;if(l.removeChild(e),n&&n.nodeType===8)if(e=n.data,e==="/$"){if(0<a&&8>a){e=a;var i=l.ownerDocument;if(e&1&&Tu(i.documentElement),e&2&&Tu(i.body),e&4)for(e=i.head,Tu(e),i=e.firstChild;i;){var c=i.nextSibling,r=i.nodeName;i[Ba]||r==="SCRIPT"||r==="STYLE"||r==="LINK"&&i.rel.toLowerCase()==="stylesheet"||e.removeChild(i),i=c}}if(u===0){l.removeChild(n),Ru(t);return}u--}else e==="$"||e==="$?"||e==="$!"?u++:a=e.charCodeAt(0)-48;else a=0;e=n}while(e);Ru(t)}function Ic(l){var t=l.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var e=t;switch(t=t.nextSibling,e.nodeName){case"HTML":case"HEAD":case"BODY":Ic(e),ui(e);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(e.rel.toLowerCase()==="stylesheet")continue}l.removeChild(e)}}function $h(l,t,e,a){for(;l.nodeType===1;){var u=e;if(l.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(l.nodeName!=="INPUT"||l.type!=="hidden"))break}else if(a){if(!l[Ba])switch(t){case"meta":if(!l.hasAttribute("itemprop"))break;return l;case"link":if(n=l.getAttribute("rel"),n==="stylesheet"&&l.hasAttribute("data-precedence"))break;if(n!==u.rel||l.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||l.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||l.getAttribute("title")!==(u.title==null?null:u.title))break;return l;case"style":if(l.hasAttribute("data-precedence"))break;return l;case"script":if(n=l.getAttribute("src"),(n!==(u.src==null?null:u.src)||l.getAttribute("type")!==(u.type==null?null:u.type)||l.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&n&&l.hasAttribute("async")&&!l.hasAttribute("itemprop"))break;return l;default:return l}}else if(t==="input"&&l.type==="hidden"){var n=u.name==null?null:""+u.name;if(u.type==="hidden"&&l.getAttribute("name")===n)return l}else return l;if(l=_t(l.nextSibling),l===null)break}return null}function Fh(l,t,e){if(t==="")return null;for(;l.nodeType!==3;)if((l.nodeType!==1||l.nodeName!=="INPUT"||l.type!=="hidden")&&!e||(l=_t(l.nextSibling),l===null))return null;return l}function lf(l){return l.data==="$!"||l.data==="$?"&&l.ownerDocument.readyState==="complete"}function Ph(l,t){var e=l.ownerDocument;if(l.data!=="$?"||e.readyState==="complete")t();else{var a=function(){t(),e.removeEventListener("DOMContentLoaded",a)};e.addEventListener("DOMContentLoaded",a),l._reactRetry=a}}function _t(l){for(;l!=null;l=l.nextSibling){var t=l.nodeType;if(t===1||t===3)break;if(t===8){if(t=l.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return l}var tf=null;function Io(l){l=l.previousSibling;for(var t=0;l;){if(l.nodeType===8){var e=l.data;if(e==="$"||e==="$!"||e==="$?"){if(t===0)return l;t--}else e==="/$"&&t++}l=l.previousSibling}return null}function ld(l,t,e){switch(t=Bn(e),l){case"html":if(l=t.documentElement,!l)throw Error(s(452));return l;case"head":if(l=t.head,!l)throw Error(s(453));return l;case"body":if(l=t.body,!l)throw Error(s(454));return l;default:throw Error(s(451))}}function Tu(l){for(var t=l.attributes;t.length;)l.removeAttributeNode(t[0]);ui(l)}var Tt=new Map,td=new Set;function Yn(l){return typeof l.getRootNode=="function"?l.getRootNode():l.nodeType===9?l:l.ownerDocument}var Wt=H.d;H.d={f:Ih,r:l0,D:t0,C:e0,L:a0,m:u0,X:i0,S:n0,M:c0};function Ih(){var l=Wt.f(),t=Dn();return l||t}function l0(l){var t=ke(l);t!==null&&t.tag===5&&t.type==="form"?Sr(t):Wt.r(l)}var Ma=typeof document>"u"?null:document;function ed(l,t,e){var a=Ma;if(a&&typeof t=="string"&&t){var u=vt(t);u='link[rel="'+l+'"][href="'+u+'"]',typeof e=="string"&&(u+='[crossorigin="'+e+'"]'),td.has(u)||(td.add(u),l={rel:l,crossOrigin:e,href:t},a.querySelector(u)===null&&(t=a.createElement("link"),kl(t,"link",l),Gl(t),a.head.appendChild(t)))}}function t0(l){Wt.D(l),ed("dns-prefetch",l,null)}function e0(l,t){Wt.C(l,t),ed("preconnect",l,t)}function a0(l,t,e){Wt.L(l,t,e);var a=Ma;if(a&&l&&t){var u='link[rel="preload"][as="'+vt(t)+'"]';t==="image"&&e&&e.imageSrcSet?(u+='[imagesrcset="'+vt(e.imageSrcSet)+'"]',typeof e.imageSizes=="string"&&(u+='[imagesizes="'+vt(e.imageSizes)+'"]')):u+='[href="'+vt(l)+'"]';var n=u;switch(t){case"style":n=Oa(l);break;case"script":n=Da(l)}Tt.has(n)||(l=R({rel:"preload",href:t==="image"&&e&&e.imageSrcSet?void 0:l,as:t},e),Tt.set(n,l),a.querySelector(u)!==null||t==="style"&&a.querySelector(Au(n))||t==="script"&&a.querySelector(Eu(n))||(t=a.createElement("link"),kl(t,"link",l),Gl(t),a.head.appendChild(t)))}}function u0(l,t){Wt.m(l,t);var e=Ma;if(e&&l){var a=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+vt(a)+'"][href="'+vt(l)+'"]',n=u;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":n=Da(l)}if(!Tt.has(n)&&(l=R({rel:"modulepreload",href:l},t),Tt.set(n,l),e.querySelector(u)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(e.querySelector(Eu(n)))return}a=e.createElement("link"),kl(a,"link",l),Gl(a),e.head.appendChild(a)}}}function n0(l,t,e){Wt.S(l,t,e);var a=Ma;if(a&&l){var u=Je(a).hoistableStyles,n=Oa(l);t=t||"default";var i=u.get(n);if(!i){var c={loading:0,preload:null};if(i=a.querySelector(Au(n)))c.loading=5;else{l=R({rel:"stylesheet",href:l,"data-precedence":t},e),(e=Tt.get(n))&&ef(l,e);var r=i=a.createElement("link");Gl(r),kl(r,"link",l),r._p=new Promise(function(v,A){r.onload=v,r.onerror=A}),r.addEventListener("load",function(){c.loading|=1}),r.addEventListener("error",function(){c.loading|=2}),c.loading|=4,Gn(i,t,a)}i={type:"stylesheet",instance:i,count:1,state:c},u.set(n,i)}}}function i0(l,t){Wt.X(l,t);var e=Ma;if(e&&l){var a=Je(e).hoistableScripts,u=Da(l),n=a.get(u);n||(n=e.querySelector(Eu(u)),n||(l=R({src:l,async:!0},t),(t=Tt.get(u))&&af(l,t),n=e.createElement("script"),Gl(n),kl(n,"link",l),e.head.appendChild(n)),n={type:"script",instance:n,count:1,state:null},a.set(u,n))}}function c0(l,t){Wt.M(l,t);var e=Ma;if(e&&l){var a=Je(e).hoistableScripts,u=Da(l),n=a.get(u);n||(n=e.querySelector(Eu(u)),n||(l=R({src:l,async:!0,type:"module"},t),(t=Tt.get(u))&&af(l,t),n=e.createElement("script"),Gl(n),kl(n,"link",l),e.head.appendChild(n)),n={type:"script",instance:n,count:1,state:null},a.set(u,n))}}function ad(l,t,e,a){var u=(u=k.current)?Yn(u):null;if(!u)throw Error(s(446));switch(l){case"meta":case"title":return null;case"style":return typeof e.precedence=="string"&&typeof e.href=="string"?(t=Oa(e.href),e=Je(u).hoistableStyles,a=e.get(t),a||(a={type:"style",instance:null,count:0,state:null},e.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(e.rel==="stylesheet"&&typeof e.href=="string"&&typeof e.precedence=="string"){l=Oa(e.href);var n=Je(u).hoistableStyles,i=n.get(l);if(i||(u=u.ownerDocument||u,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},n.set(l,i),(n=u.querySelector(Au(l)))&&!n._p&&(i.instance=n,i.state.loading=5),Tt.has(l)||(e={rel:"preload",as:"style",href:e.href,crossOrigin:e.crossOrigin,integrity:e.integrity,media:e.media,hrefLang:e.hrefLang,referrerPolicy:e.referrerPolicy},Tt.set(l,e),n||f0(u,l,e,i.state))),t&&a===null)throw Error(s(528,""));return i}if(t&&a!==null)throw Error(s(529,""));return null;case"script":return t=e.async,e=e.src,typeof e=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Da(e),e=Je(u).hoistableScripts,a=e.get(t),a||(a={type:"script",instance:null,count:0,state:null},e.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,l))}}function Oa(l){return'href="'+vt(l)+'"'}function Au(l){return'link[rel="stylesheet"]['+l+"]"}function ud(l){return R({},l,{"data-precedence":l.precedence,precedence:null})}function f0(l,t,e,a){l.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=l.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),kl(t,"link",e),Gl(t),l.head.appendChild(t))}function Da(l){return'[src="'+vt(l)+'"]'}function Eu(l){return"script[async]"+l}function nd(l,t,e){if(t.count++,t.instance===null)switch(t.type){case"style":var a=l.querySelector('style[data-href~="'+vt(e.href)+'"]');if(a)return t.instance=a,Gl(a),a;var u=R({},e,{"data-href":e.href,"data-precedence":e.precedence,href:null,precedence:null});return a=(l.ownerDocument||l).createElement("style"),Gl(a),kl(a,"style",u),Gn(a,e.precedence,l),t.instance=a;case"stylesheet":u=Oa(e.href);var n=l.querySelector(Au(u));if(n)return t.state.loading|=4,t.instance=n,Gl(n),n;a=ud(e),(u=Tt.get(u))&&ef(a,u),n=(l.ownerDocument||l).createElement("link"),Gl(n);var i=n;return i._p=new Promise(function(c,r){i.onload=c,i.onerror=r}),kl(n,"link",a),t.state.loading|=4,Gn(n,e.precedence,l),t.instance=n;case"script":return n=Da(e.src),(u=l.querySelector(Eu(n)))?(t.instance=u,Gl(u),u):(a=e,(u=Tt.get(n))&&(a=R({},e),af(a,u)),l=l.ownerDocument||l,u=l.createElement("script"),Gl(u),kl(u,"link",a),l.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,Gn(a,e.precedence,l));return t.instance}function Gn(l,t,e){for(var a=e.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=a.length?a[a.length-1]:null,n=u,i=0;i<a.length;i++){var c=a[i];if(c.dataset.precedence===t)n=c;else if(n!==u)break}n?n.parentNode.insertBefore(l,n.nextSibling):(t=e.nodeType===9?e.head:e,t.insertBefore(l,t.firstChild))}function ef(l,t){l.crossOrigin==null&&(l.crossOrigin=t.crossOrigin),l.referrerPolicy==null&&(l.referrerPolicy=t.referrerPolicy),l.title==null&&(l.title=t.title)}function af(l,t){l.crossOrigin==null&&(l.crossOrigin=t.crossOrigin),l.referrerPolicy==null&&(l.referrerPolicy=t.referrerPolicy),l.integrity==null&&(l.integrity=t.integrity)}var wn=null;function id(l,t,e){if(wn===null){var a=new Map,u=wn=new Map;u.set(e,a)}else u=wn,a=u.get(e),a||(a=new Map,u.set(e,a));if(a.has(l))return a;for(a.set(l,null),e=e.getElementsByTagName(l),u=0;u<e.length;u++){var n=e[u];if(!(n[Ba]||n[Jl]||l==="link"&&n.getAttribute("rel")==="stylesheet")&&n.namespaceURI!=="http://www.w3.org/2000/svg"){var i=n.getAttribute(t)||"";i=l+i;var c=a.get(i);c?c.push(n):a.set(i,[n])}}return a}function cd(l,t,e){l=l.ownerDocument||l,l.head.insertBefore(e,t==="title"?l.querySelector("head > title"):null)}function s0(l,t,e){if(e===1||t.itemProp!=null)return!1;switch(l){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return l=t.disabled,typeof t.precedence=="string"&&l==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function fd(l){return!(l.type==="stylesheet"&&(l.state.loading&3)===0)}var zu=null;function r0(){}function o0(l,t,e){if(zu===null)throw Error(s(475));var a=zu;if(t.type==="stylesheet"&&(typeof e.media!="string"||matchMedia(e.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=Oa(e.href),n=l.querySelector(Au(u));if(n){l=n._p,l!==null&&typeof l=="object"&&typeof l.then=="function"&&(a.count++,a=Xn.bind(a),l.then(a,a)),t.state.loading|=4,t.instance=n,Gl(n);return}n=l.ownerDocument||l,e=ud(e),(u=Tt.get(u))&&ef(e,u),n=n.createElement("link"),Gl(n);var i=n;i._p=new Promise(function(c,r){i.onload=c,i.onerror=r}),kl(n,"link",e),t.instance=n}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,l),(l=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=Xn.bind(a),l.addEventListener("load",t),l.addEventListener("error",t))}}function d0(){if(zu===null)throw Error(s(475));var l=zu;return l.stylesheets&&l.count===0&&uf(l,l.stylesheets),0<l.count?function(t){var e=setTimeout(function(){if(l.stylesheets&&uf(l,l.stylesheets),l.unsuspend){var a=l.unsuspend;l.unsuspend=null,a()}},6e4);return l.unsuspend=t,function(){l.unsuspend=null,clearTimeout(e)}}:null}function Xn(){if(this.count--,this.count===0){if(this.stylesheets)uf(this,this.stylesheets);else if(this.unsuspend){var l=this.unsuspend;this.unsuspend=null,l()}}}var Qn=null;function uf(l,t){l.stylesheets=null,l.unsuspend!==null&&(l.count++,Qn=new Map,t.forEach(m0,l),Qn=null,Xn.call(l))}function m0(l,t){if(!(t.state.loading&4)){var e=Qn.get(l);if(e)var a=e.get(null);else{e=new Map,Qn.set(l,e);for(var u=l.querySelectorAll("link[data-precedence],style[data-precedence]"),n=0;n<u.length;n++){var i=u[n];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(e.set(i.dataset.precedence,i),a=i)}a&&e.set(null,a)}u=t.instance,i=u.getAttribute("data-precedence"),n=e.get(i)||a,n===a&&e.set(null,u),e.set(i,u),this.count++,a=Xn.bind(this),u.addEventListener("load",a),u.addEventListener("error",a),n?n.parentNode.insertBefore(u,n.nextSibling):(l=l.nodeType===9?l.head:l,l.insertBefore(u,l.firstChild)),t.state.loading|=4}}var _u={$$typeof:ml,Provider:null,Consumer:null,_currentValue:D,_currentValue2:D,_threadCount:0};function h0(l,t,e,a,u,n,i,c){this.tag=1,this.containerInfo=l,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=li(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=li(0),this.hiddenUpdates=li(null),this.identifierPrefix=a,this.onUncaughtError=u,this.onCaughtError=n,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=c,this.incompleteTransitions=new Map}function sd(l,t,e,a,u,n,i,c,r,v,A,z){return l=new h0(l,t,e,i,c,r,v,z),t=1,n===!0&&(t|=24),n=ct(3,null,null,t),l.current=n,n.stateNode=l,t=Gi(),t.refCount++,l.pooledCache=t,t.refCount++,n.memoizedState={element:a,isDehydrated:e,cache:t},Zi(n),l}function rd(l){return l?(l=ia,l):ia}function od(l,t,e,a,u,n){u=rd(u),a.context===null?a.context=u:a.pendingContext=u,a=ne(t),a.payload={element:e},n=n===void 0?null:n,n!==null&&(a.callback=n),e=ie(l,a,t),e!==null&&(dt(e,l,t),eu(e,l,t))}function dd(l,t){if(l=l.memoizedState,l!==null&&l.dehydrated!==null){var e=l.retryLane;l.retryLane=e!==0&&e<t?e:t}}function nf(l,t){dd(l,t),(l=l.alternate)&&dd(l,t)}function md(l){if(l.tag===13){var t=na(l,67108864);t!==null&&dt(t,l,67108864),nf(l,67108864)}}var Zn=!0;function v0(l,t,e,a){var u=x.T;x.T=null;var n=H.p;try{H.p=2,cf(l,t,e,a)}finally{H.p=n,x.T=u}}function y0(l,t,e,a){var u=x.T;x.T=null;var n=H.p;try{H.p=8,cf(l,t,e,a)}finally{H.p=n,x.T=u}}function cf(l,t,e,a){if(Zn){var u=ff(a);if(u===null)kc(l,t,a,Vn,e),vd(l,a);else if(b0(u,l,t,e,a))a.stopPropagation();else if(vd(l,a),t&4&&-1<g0.indexOf(l)){for(;u!==null;){var n=ke(u);if(n!==null)switch(n.tag){case 3:if(n=n.stateNode,n.current.memoizedState.isDehydrated){var i=ze(n.pendingLanes);if(i!==0){var c=n;for(c.pendingLanes|=2,c.entangledLanes|=2;i;){var r=1<<31-nt(i);c.entanglements[1]|=r,i&=~r}Ht(n),(ol&6)===0&&(Mn=Ot()+500,pu(0))}}break;case 13:c=na(n,2),c!==null&&dt(c,n,2),Dn(),nf(n,2)}if(n=ff(a),n===null&&kc(l,t,a,Vn,e),n===u)break;u=n}u!==null&&a.stopPropagation()}else kc(l,t,a,null,e)}}function ff(l){return l=di(l),sf(l)}var Vn=null;function sf(l){if(Vn=null,l=Ke(l),l!==null){var t=O(l);if(t===null)l=null;else{var e=t.tag;if(e===13){if(l=U(t),l!==null)return l;l=null}else if(e===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;l=null}else t!==l&&(l=null)}}return Vn=l,null}function hd(l){switch(l){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(em()){case Mf:return 2;case Of:return 8;case Cu:case am:return 32;case Df:return 268435456;default:return 32}default:return 32}}var rf=!1,pe=null,Se=null,xe=null,Mu=new Map,Ou=new Map,Te=[],g0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function vd(l,t){switch(l){case"focusin":case"focusout":pe=null;break;case"dragenter":case"dragleave":Se=null;break;case"mouseover":case"mouseout":xe=null;break;case"pointerover":case"pointerout":Mu.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ou.delete(t.pointerId)}}function Du(l,t,e,a,u,n){return l===null||l.nativeEvent!==n?(l={blockedOn:t,domEventName:e,eventSystemFlags:a,nativeEvent:n,targetContainers:[u]},t!==null&&(t=ke(t),t!==null&&md(t)),l):(l.eventSystemFlags|=a,t=l.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),l)}function b0(l,t,e,a,u){switch(t){case"focusin":return pe=Du(pe,l,t,e,a,u),!0;case"dragenter":return Se=Du(Se,l,t,e,a,u),!0;case"mouseover":return xe=Du(xe,l,t,e,a,u),!0;case"pointerover":var n=u.pointerId;return Mu.set(n,Du(Mu.get(n)||null,l,t,e,a,u)),!0;case"gotpointercapture":return n=u.pointerId,Ou.set(n,Du(Ou.get(n)||null,l,t,e,a,u)),!0}return!1}function yd(l){var t=Ke(l.target);if(t!==null){var e=O(t);if(e!==null){if(t=e.tag,t===13){if(t=U(e),t!==null){l.blockedOn=t,om(l.priority,function(){if(e.tag===13){var a=ot();a=ti(a);var u=na(e,a);u!==null&&dt(u,e,a),nf(e,a)}});return}}else if(t===3&&e.stateNode.current.memoizedState.isDehydrated){l.blockedOn=e.tag===3?e.stateNode.containerInfo:null;return}}}l.blockedOn=null}function Ln(l){if(l.blockedOn!==null)return!1;for(var t=l.targetContainers;0<t.length;){var e=ff(l.nativeEvent);if(e===null){e=l.nativeEvent;var a=new e.constructor(e.type,e);oi=a,e.target.dispatchEvent(a),oi=null}else return t=ke(e),t!==null&&md(t),l.blockedOn=e,!1;t.shift()}return!0}function gd(l,t,e){Ln(l)&&e.delete(t)}function p0(){rf=!1,pe!==null&&Ln(pe)&&(pe=null),Se!==null&&Ln(Se)&&(Se=null),xe!==null&&Ln(xe)&&(xe=null),Mu.forEach(gd),Ou.forEach(gd)}function Kn(l,t){l.blockedOn===t&&(l.blockedOn=null,rf||(rf=!0,f.unstable_scheduleCallback(f.unstable_NormalPriority,p0)))}var kn=null;function bd(l){kn!==l&&(kn=l,f.unstable_scheduleCallback(f.unstable_NormalPriority,function(){kn===l&&(kn=null);for(var t=0;t<l.length;t+=3){var e=l[t],a=l[t+1],u=l[t+2];if(typeof a!="function"){if(sf(a||e)===null)continue;break}var n=ke(e);n!==null&&(l.splice(t,3),t-=3,fc(n,{pending:!0,data:u,method:e.method,action:a},a,u))}}))}function Ru(l){function t(r){return Kn(r,l)}pe!==null&&Kn(pe,l),Se!==null&&Kn(Se,l),xe!==null&&Kn(xe,l),Mu.forEach(t),Ou.forEach(t);for(var e=0;e<Te.length;e++){var a=Te[e];a.blockedOn===l&&(a.blockedOn=null)}for(;0<Te.length&&(e=Te[0],e.blockedOn===null);)yd(e),e.blockedOn===null&&Te.shift();if(e=(l.ownerDocument||l).$$reactFormReplay,e!=null)for(a=0;a<e.length;a+=3){var u=e[a],n=e[a+1],i=u[Pl]||null;if(typeof n=="function")i||bd(e);else if(i){var c=null;if(n&&n.hasAttribute("formAction")){if(u=n,i=n[Pl]||null)c=i.formAction;else if(sf(u)!==null)continue}else c=i.action;typeof c=="function"?e[a+1]=c:(e.splice(a,3),a-=3),bd(e)}}}function of(l){this._internalRoot=l}Jn.prototype.render=of.prototype.render=function(l){var t=this._internalRoot;if(t===null)throw Error(s(409));var e=t.current,a=ot();od(e,a,l,t,null,null)},Jn.prototype.unmount=of.prototype.unmount=function(){var l=this._internalRoot;if(l!==null){this._internalRoot=null;var t=l.containerInfo;od(l.current,2,null,l,null,null),Dn(),t[Le]=null}};function Jn(l){this._internalRoot=l}Jn.prototype.unstable_scheduleHydration=function(l){if(l){var t=jf();l={blockedOn:null,target:l,priority:t};for(var e=0;e<Te.length&&t!==0&&t<Te[e].priority;e++);Te.splice(e,0,l),e===0&&yd(l)}};var pd=p.version;if(pd!=="19.1.0")throw Error(s(527,pd,"19.1.0"));H.findDOMNode=function(l){var t=l._reactInternals;if(t===void 0)throw typeof l.render=="function"?Error(s(188)):(l=Object.keys(l).join(","),Error(s(268,l)));return l=_(t),l=l!==null?S(l):null,l=l===null?null:l.stateNode,l};var S0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:x,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Wn=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Wn.isDisabled&&Wn.supportsFiber)try{ja=Wn.inject(S0),ut=Wn}catch{}}return Uu.createRoot=function(l,t){if(!T(l))throw Error(s(299));var e=!1,a="",u=jr,n=Cr,i=qr,c=null;return t!=null&&(t.unstable_strictMode===!0&&(e=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(n=t.onCaughtError),t.onRecoverableError!==void 0&&(i=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(c=t.unstable_transitionCallbacks)),t=sd(l,1,!1,null,null,e,a,u,n,i,c,null),l[Le]=t.current,Kc(l),new of(t)},Uu.hydrateRoot=function(l,t,e){if(!T(l))throw Error(s(299));var a=!1,u="",n=jr,i=Cr,c=qr,r=null,v=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(u=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(i=e.onCaughtError),e.onRecoverableError!==void 0&&(c=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(r=e.unstable_transitionCallbacks),e.formState!==void 0&&(v=e.formState)),t=sd(l,1,!0,t,e??null,a,u,n,i,c,r,v),t.context=rd(null),e=t.current,a=ot(),a=ti(a),u=ne(a),u.callback=null,ie(e,u,a),e=a,t.current.lanes=e,qa(t,e),Ht(t),l[Le]=t.current,Kc(l),new Jn(t)},Uu.version="19.1.0",Uu}var Dd;function R0(){if(Dd)return hf.exports;Dd=1;function f(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(f)}catch(p){console.error(p)}}return f(),hf.exports=D0(),hf.exports}var N0=R0();/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U0=f=>f.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),H0=f=>f.replace(/^([A-Z])|[\s-_]+(\w)/g,(p,y,s)=>s?s.toUpperCase():y.toLowerCase()),Rd=f=>{const p=H0(f);return p.charAt(0).toUpperCase()+p.slice(1)},Gd=(...f)=>f.filter((p,y,s)=>!!p&&p.trim()!==""&&s.indexOf(p)===y).join(" ").trim(),j0=f=>{for(const p in f)if(p.startsWith("aria-")||p==="role"||p==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var C0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q0=pl.forwardRef(({color:f="currentColor",size:p=24,strokeWidth:y=2,absoluteStrokeWidth:s,className:T="",children:O,iconNode:U,...w},_)=>pl.createElement("svg",{ref:_,...C0,width:p,height:p,stroke:f,strokeWidth:s?Number(y)*24/Number(p):y,className:Gd("lucide",T),...!O&&!j0(w)&&{"aria-hidden":"true"},...w},[...U.map(([S,R])=>pl.createElement(S,R)),...Array.isArray(O)?O:[O]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Na=(f,p)=>{const y=pl.forwardRef(({className:s,...T},O)=>pl.createElement(q0,{ref:O,iconNode:p,className:Gd(`lucide-${U0(Rd(f))}`,`lucide-${f}`,s),...T}));return y.displayName=Rd(f),y};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B0=[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]],Nd=Na("book",B0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y0=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]],G0=Na("download",Y0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w0=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],X0=Na("globe",w0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Q0=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Z0=Na("search",Q0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V0=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],L0=Na("tag",V0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K0=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],k0=Na("user",K0);function Ud(f,p){if(typeof f=="function")return f(p);f!=null&&(f.current=p)}function J0(...f){return p=>{let y=!1;const s=f.map(T=>{const O=Ud(T,p);return!y&&typeof O=="function"&&(y=!0),O});if(y)return()=>{for(let T=0;T<s.length;T++){const O=s[T];typeof O=="function"?O():Ud(f[T],null)}}}}function W0(...f){return pl.useCallback(J0(...f),f)}function $0(f){const p=F0(f),y=pl.forwardRef((s,T)=>{const{children:O,...U}=s,w=pl.Children.toArray(O),_=w.find(I0);if(_){const S=_.props.children,R=w.map(J=>J===_?pl.Children.count(S)>1?pl.Children.only(null):pl.isValidElement(S)?S.props.children:null:J);return C.jsx(p,{...U,ref:T,children:pl.isValidElement(S)?pl.cloneElement(S,void 0,R):null})}return C.jsx(p,{...U,ref:T,children:O})});return y.displayName=`${f}.Slot`,y}var wd=$0("Slot");function F0(f){const p=pl.forwardRef((y,s)=>{const{children:T,...O}=y,U=pl.isValidElement(T)?tv(T):void 0,w=W0(U,s);if(pl.isValidElement(T)){const _=lv(O,T.props);return T.type!==pl.Fragment&&(_.ref=w),pl.cloneElement(T,_)}return pl.Children.count(T)>1?pl.Children.only(null):null});return p.displayName=`${f}.SlotClone`,p}var P0=Symbol("radix.slottable");function I0(f){return pl.isValidElement(f)&&typeof f.type=="function"&&"__radixId"in f.type&&f.type.__radixId===P0}function lv(f,p){const y={...p};for(const s in p){const T=f[s],O=p[s];/^on[A-Z]/.test(s)?T&&O?y[s]=(...w)=>{const _=O(...w);return T(...w),_}:T&&(y[s]=T):s==="style"?y[s]={...T,...O}:s==="className"&&(y[s]=[T,O].filter(Boolean).join(" "))}return{...f,...y}}function tv(f){var s,T;let p=(s=Object.getOwnPropertyDescriptor(f.props,"ref"))==null?void 0:s.get,y=p&&"isReactWarning"in p&&p.isReactWarning;return y?f.ref:(p=(T=Object.getOwnPropertyDescriptor(f,"ref"))==null?void 0:T.get,y=p&&"isReactWarning"in p&&p.isReactWarning,y?f.props.ref:f.props.ref||f.ref)}function Xd(f){var p,y,s="";if(typeof f=="string"||typeof f=="number")s+=f;else if(typeof f=="object")if(Array.isArray(f)){var T=f.length;for(p=0;p<T;p++)f[p]&&(y=Xd(f[p]))&&(s&&(s+=" "),s+=y)}else for(y in f)f[y]&&(s&&(s+=" "),s+=y);return s}function Qd(){for(var f,p,y=0,s="",T=arguments.length;y<T;y++)(f=arguments[y])&&(p=Xd(f))&&(s&&(s+=" "),s+=p);return s}const Hd=f=>typeof f=="boolean"?`${f}`:f===0?"0":f,jd=Qd,Zd=(f,p)=>y=>{var s;if((p==null?void 0:p.variants)==null)return jd(f,y==null?void 0:y.class,y==null?void 0:y.className);const{variants:T,defaultVariants:O}=p,U=Object.keys(T).map(S=>{const R=y==null?void 0:y[S],J=O==null?void 0:O[S];if(R===null)return null;const $=Hd(R)||Hd(J);return T[S][$]}),w=y&&Object.entries(y).reduce((S,R)=>{let[J,$]=R;return $===void 0||(S[J]=$),S},{}),_=p==null||(s=p.compoundVariants)===null||s===void 0?void 0:s.reduce((S,R)=>{let{class:J,className:$,...xl}=R;return Object.entries(xl).every(Z=>{let[W,nl]=Z;return Array.isArray(nl)?nl.includes({...O,...w}[W]):{...O,...w}[W]===nl})?[...S,J,$]:S},[]);return jd(f,U,_,y==null?void 0:y.class,y==null?void 0:y.className)},_f="-",ev=f=>{const p=uv(f),{conflictingClassGroups:y,conflictingClassGroupModifiers:s}=f;return{getClassGroupId:U=>{const w=U.split(_f);return w[0]===""&&w.length!==1&&w.shift(),Vd(w,p)||av(U)},getConflictingClassGroupIds:(U,w)=>{const _=y[U]||[];return w&&s[U]?[..._,...s[U]]:_}}},Vd=(f,p)=>{var U;if(f.length===0)return p.classGroupId;const y=f[0],s=p.nextPart.get(y),T=s?Vd(f.slice(1),s):void 0;if(T)return T;if(p.validators.length===0)return;const O=f.join(_f);return(U=p.validators.find(({validator:w})=>w(O)))==null?void 0:U.classGroupId},Cd=/^\[(.+)\]$/,av=f=>{if(Cd.test(f)){const p=Cd.exec(f)[1],y=p==null?void 0:p.substring(0,p.indexOf(":"));if(y)return"arbitrary.."+y}},uv=f=>{const{theme:p,classGroups:y}=f,s={nextPart:new Map,validators:[]};for(const T in y)Tf(y[T],s,T,p);return s},Tf=(f,p,y,s)=>{f.forEach(T=>{if(typeof T=="string"){const O=T===""?p:qd(p,T);O.classGroupId=y;return}if(typeof T=="function"){if(nv(T)){Tf(T(s),p,y,s);return}p.validators.push({validator:T,classGroupId:y});return}Object.entries(T).forEach(([O,U])=>{Tf(U,qd(p,O),y,s)})})},qd=(f,p)=>{let y=f;return p.split(_f).forEach(s=>{y.nextPart.has(s)||y.nextPart.set(s,{nextPart:new Map,validators:[]}),y=y.nextPart.get(s)}),y},nv=f=>f.isThemeGetter,iv=f=>{if(f<1)return{get:()=>{},set:()=>{}};let p=0,y=new Map,s=new Map;const T=(O,U)=>{y.set(O,U),p++,p>f&&(p=0,s=y,y=new Map)};return{get(O){let U=y.get(O);if(U!==void 0)return U;if((U=s.get(O))!==void 0)return T(O,U),U},set(O,U){y.has(O)?y.set(O,U):T(O,U)}}},Af="!",Ef=":",cv=Ef.length,fv=f=>{const{prefix:p,experimentalParseClassName:y}=f;let s=T=>{const O=[];let U=0,w=0,_=0,S;for(let Z=0;Z<T.length;Z++){let W=T[Z];if(U===0&&w===0){if(W===Ef){O.push(T.slice(_,Z)),_=Z+cv;continue}if(W==="/"){S=Z;continue}}W==="["?U++:W==="]"?U--:W==="("?w++:W===")"&&w--}const R=O.length===0?T:T.substring(_),J=sv(R),$=J!==R,xl=S&&S>_?S-_:void 0;return{modifiers:O,hasImportantModifier:$,baseClassName:J,maybePostfixModifierPosition:xl}};if(p){const T=p+Ef,O=s;s=U=>U.startsWith(T)?O(U.substring(T.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:U,maybePostfixModifierPosition:void 0}}if(y){const T=s;s=O=>y({className:O,parseClassName:T})}return s},sv=f=>f.endsWith(Af)?f.substring(0,f.length-1):f.startsWith(Af)?f.substring(1):f,rv=f=>{const p=Object.fromEntries(f.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const T=[];let O=[];return s.forEach(U=>{U[0]==="["||p[U]?(T.push(...O.sort(),U),O=[]):O.push(U)}),T.push(...O.sort()),T}},ov=f=>({cache:iv(f.cacheSize),parseClassName:fv(f),sortModifiers:rv(f),...ev(f)}),dv=/\s+/,mv=(f,p)=>{const{parseClassName:y,getClassGroupId:s,getConflictingClassGroupIds:T,sortModifiers:O}=p,U=[],w=f.trim().split(dv);let _="";for(let S=w.length-1;S>=0;S-=1){const R=w[S],{isExternal:J,modifiers:$,hasImportantModifier:xl,baseClassName:Z,maybePostfixModifierPosition:W}=y(R);if(J){_=R+(_.length>0?" "+_:_);continue}let nl=!!W,Zl=s(nl?Z.substring(0,W):Z);if(!Zl){if(!nl){_=R+(_.length>0?" "+_:_);continue}if(Zl=s(Z),!Zl){_=R+(_.length>0?" "+_:_);continue}nl=!1}const Vl=O($).join(":"),ml=xl?Vl+Af:Vl,Yl=ml+Zl;if(U.includes(Yl))continue;U.push(Yl);const P=T(Zl,nl);for(let Ml=0;Ml<P.length;++Ml){const jl=P[Ml];U.push(ml+jl)}_=R+(_.length>0?" "+_:_)}return _};function hv(){let f=0,p,y,s="";for(;f<arguments.length;)(p=arguments[f++])&&(y=Ld(p))&&(s&&(s+=" "),s+=y);return s}const Ld=f=>{if(typeof f=="string")return f;let p,y="";for(let s=0;s<f.length;s++)f[s]&&(p=Ld(f[s]))&&(y&&(y+=" "),y+=p);return y};function vv(f,...p){let y,s,T,O=U;function U(_){const S=p.reduce((R,J)=>J(R),f());return y=ov(S),s=y.cache.get,T=y.cache.set,O=w,w(_)}function w(_){const S=s(_);if(S)return S;const R=mv(_,y);return T(_,R),R}return function(){return O(hv.apply(null,arguments))}}const Bl=f=>{const p=y=>y[f]||[];return p.isThemeGetter=!0,p},Kd=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,kd=/^\((?:(\w[\w-]*):)?(.+)\)$/i,yv=/^\d+\/\d+$/,gv=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,bv=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,pv=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Sv=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,xv=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ra=f=>yv.test(f),ll=f=>!!f&&!Number.isNaN(Number(f)),Ee=f=>!!f&&Number.isInteger(Number(f)),bf=f=>f.endsWith("%")&&ll(f.slice(0,-1)),$t=f=>gv.test(f),Tv=()=>!0,Av=f=>bv.test(f)&&!pv.test(f),Jd=()=>!1,Ev=f=>Sv.test(f),zv=f=>xv.test(f),_v=f=>!Y(f)&&!G(f),Mv=f=>Ua(f,Fd,Jd),Y=f=>Kd.test(f),Ze=f=>Ua(f,Pd,Av),pf=f=>Ua(f,Uv,ll),Bd=f=>Ua(f,Wd,Jd),Ov=f=>Ua(f,$d,zv),$n=f=>Ua(f,Id,Ev),G=f=>kd.test(f),Hu=f=>Ha(f,Pd),Dv=f=>Ha(f,Hv),Yd=f=>Ha(f,Wd),Rv=f=>Ha(f,Fd),Nv=f=>Ha(f,$d),Fn=f=>Ha(f,Id,!0),Ua=(f,p,y)=>{const s=Kd.exec(f);return s?s[1]?p(s[1]):y(s[2]):!1},Ha=(f,p,y=!1)=>{const s=kd.exec(f);return s?s[1]?p(s[1]):y:!1},Wd=f=>f==="position"||f==="percentage",$d=f=>f==="image"||f==="url",Fd=f=>f==="length"||f==="size"||f==="bg-size",Pd=f=>f==="length",Uv=f=>f==="number",Hv=f=>f==="family-name",Id=f=>f==="shadow",jv=()=>{const f=Bl("color"),p=Bl("font"),y=Bl("text"),s=Bl("font-weight"),T=Bl("tracking"),O=Bl("leading"),U=Bl("breakpoint"),w=Bl("container"),_=Bl("spacing"),S=Bl("radius"),R=Bl("shadow"),J=Bl("inset-shadow"),$=Bl("text-shadow"),xl=Bl("drop-shadow"),Z=Bl("blur"),W=Bl("perspective"),nl=Bl("aspect"),Zl=Bl("ease"),Vl=Bl("animate"),ml=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Yl=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],P=()=>[...Yl(),G,Y],Ml=()=>["auto","hidden","clip","visible","scroll"],jl=()=>["auto","contain","none"],Q=()=>[G,Y,_],Nl=()=>[Ra,"full","auto",...Q()],jt=()=>[Ee,"none","subgrid",G,Y],mt=()=>["auto",{span:["full",Ee,G,Y]},Ee,G,Y],Ol=()=>[Ee,"auto",G,Y],Mt=()=>["auto","min","max","fr",G,Y],At=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Al=()=>["start","end","center","stretch","center-safe","end-safe"],x=()=>["auto",...Q()],H=()=>[Ra,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Q()],D=()=>[f,G,Y],rl=()=>[...Yl(),Yd,Bd,{position:[G,Y]}],o=()=>["no-repeat",{repeat:["","x","y","space","round"]}],M=()=>["auto","cover","contain",Rv,Mv,{size:[G,Y]}],j=()=>[bf,Hu,Ze],N=()=>["","none","full",S,G,Y],q=()=>["",ll,Hu,Ze],el=()=>["solid","dashed","dotted","double"],k=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],cl=()=>[ll,bf,Yd,Bd],gl=()=>["","none",Z,G,Y],at=()=>["none",ll,G,Y],Ft=()=>["none",ll,G,Y],Pt=()=>[ll,G,Y],It=()=>[Ra,"full",...Q()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[$t],breakpoint:[$t],color:[Tv],container:[$t],"drop-shadow":[$t],ease:["in","out","in-out"],font:[_v],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[$t],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[$t],shadow:[$t],spacing:["px",ll],text:[$t],"text-shadow":[$t],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ra,Y,G,nl]}],container:["container"],columns:[{columns:[ll,Y,G,w]}],"break-after":[{"break-after":ml()}],"break-before":[{"break-before":ml()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:P()}],overflow:[{overflow:Ml()}],"overflow-x":[{"overflow-x":Ml()}],"overflow-y":[{"overflow-y":Ml()}],overscroll:[{overscroll:jl()}],"overscroll-x":[{"overscroll-x":jl()}],"overscroll-y":[{"overscroll-y":jl()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Nl()}],"inset-x":[{"inset-x":Nl()}],"inset-y":[{"inset-y":Nl()}],start:[{start:Nl()}],end:[{end:Nl()}],top:[{top:Nl()}],right:[{right:Nl()}],bottom:[{bottom:Nl()}],left:[{left:Nl()}],visibility:["visible","invisible","collapse"],z:[{z:[Ee,"auto",G,Y]}],basis:[{basis:[Ra,"full","auto",w,...Q()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ll,Ra,"auto","initial","none",Y]}],grow:[{grow:["",ll,G,Y]}],shrink:[{shrink:["",ll,G,Y]}],order:[{order:[Ee,"first","last","none",G,Y]}],"grid-cols":[{"grid-cols":jt()}],"col-start-end":[{col:mt()}],"col-start":[{"col-start":Ol()}],"col-end":[{"col-end":Ol()}],"grid-rows":[{"grid-rows":jt()}],"row-start-end":[{row:mt()}],"row-start":[{"row-start":Ol()}],"row-end":[{"row-end":Ol()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Mt()}],"auto-rows":[{"auto-rows":Mt()}],gap:[{gap:Q()}],"gap-x":[{"gap-x":Q()}],"gap-y":[{"gap-y":Q()}],"justify-content":[{justify:[...At(),"normal"]}],"justify-items":[{"justify-items":[...Al(),"normal"]}],"justify-self":[{"justify-self":["auto",...Al()]}],"align-content":[{content:["normal",...At()]}],"align-items":[{items:[...Al(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Al(),{baseline:["","last"]}]}],"place-content":[{"place-content":At()}],"place-items":[{"place-items":[...Al(),"baseline"]}],"place-self":[{"place-self":["auto",...Al()]}],p:[{p:Q()}],px:[{px:Q()}],py:[{py:Q()}],ps:[{ps:Q()}],pe:[{pe:Q()}],pt:[{pt:Q()}],pr:[{pr:Q()}],pb:[{pb:Q()}],pl:[{pl:Q()}],m:[{m:x()}],mx:[{mx:x()}],my:[{my:x()}],ms:[{ms:x()}],me:[{me:x()}],mt:[{mt:x()}],mr:[{mr:x()}],mb:[{mb:x()}],ml:[{ml:x()}],"space-x":[{"space-x":Q()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Q()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[w,"screen",...H()]}],"min-w":[{"min-w":[w,"screen","none",...H()]}],"max-w":[{"max-w":[w,"screen","none","prose",{screen:[U]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",y,Hu,Ze]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,G,pf]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",bf,Y]}],"font-family":[{font:[Dv,Y,p]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[T,G,Y]}],"line-clamp":[{"line-clamp":[ll,"none",G,pf]}],leading:[{leading:[O,...Q()]}],"list-image":[{"list-image":["none",G,Y]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,Y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:D()}],"text-color":[{text:D()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...el(),"wavy"]}],"text-decoration-thickness":[{decoration:[ll,"from-font","auto",G,Ze]}],"text-decoration-color":[{decoration:D()}],"underline-offset":[{"underline-offset":[ll,"auto",G,Y]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,Y]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,Y]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:rl()}],"bg-repeat":[{bg:o()}],"bg-size":[{bg:M()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ee,G,Y],radial:["",G,Y],conic:[Ee,G,Y]},Nv,Ov]}],"bg-color":[{bg:D()}],"gradient-from-pos":[{from:j()}],"gradient-via-pos":[{via:j()}],"gradient-to-pos":[{to:j()}],"gradient-from":[{from:D()}],"gradient-via":[{via:D()}],"gradient-to":[{to:D()}],rounded:[{rounded:N()}],"rounded-s":[{"rounded-s":N()}],"rounded-e":[{"rounded-e":N()}],"rounded-t":[{"rounded-t":N()}],"rounded-r":[{"rounded-r":N()}],"rounded-b":[{"rounded-b":N()}],"rounded-l":[{"rounded-l":N()}],"rounded-ss":[{"rounded-ss":N()}],"rounded-se":[{"rounded-se":N()}],"rounded-ee":[{"rounded-ee":N()}],"rounded-es":[{"rounded-es":N()}],"rounded-tl":[{"rounded-tl":N()}],"rounded-tr":[{"rounded-tr":N()}],"rounded-br":[{"rounded-br":N()}],"rounded-bl":[{"rounded-bl":N()}],"border-w":[{border:q()}],"border-w-x":[{"border-x":q()}],"border-w-y":[{"border-y":q()}],"border-w-s":[{"border-s":q()}],"border-w-e":[{"border-e":q()}],"border-w-t":[{"border-t":q()}],"border-w-r":[{"border-r":q()}],"border-w-b":[{"border-b":q()}],"border-w-l":[{"border-l":q()}],"divide-x":[{"divide-x":q()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":q()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...el(),"hidden","none"]}],"divide-style":[{divide:[...el(),"hidden","none"]}],"border-color":[{border:D()}],"border-color-x":[{"border-x":D()}],"border-color-y":[{"border-y":D()}],"border-color-s":[{"border-s":D()}],"border-color-e":[{"border-e":D()}],"border-color-t":[{"border-t":D()}],"border-color-r":[{"border-r":D()}],"border-color-b":[{"border-b":D()}],"border-color-l":[{"border-l":D()}],"divide-color":[{divide:D()}],"outline-style":[{outline:[...el(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ll,G,Y]}],"outline-w":[{outline:["",ll,Hu,Ze]}],"outline-color":[{outline:D()}],shadow:[{shadow:["","none",R,Fn,$n]}],"shadow-color":[{shadow:D()}],"inset-shadow":[{"inset-shadow":["none",J,Fn,$n]}],"inset-shadow-color":[{"inset-shadow":D()}],"ring-w":[{ring:q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:D()}],"ring-offset-w":[{"ring-offset":[ll,Ze]}],"ring-offset-color":[{"ring-offset":D()}],"inset-ring-w":[{"inset-ring":q()}],"inset-ring-color":[{"inset-ring":D()}],"text-shadow":[{"text-shadow":["none",$,Fn,$n]}],"text-shadow-color":[{"text-shadow":D()}],opacity:[{opacity:[ll,G,Y]}],"mix-blend":[{"mix-blend":[...k(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":k()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ll]}],"mask-image-linear-from-pos":[{"mask-linear-from":cl()}],"mask-image-linear-to-pos":[{"mask-linear-to":cl()}],"mask-image-linear-from-color":[{"mask-linear-from":D()}],"mask-image-linear-to-color":[{"mask-linear-to":D()}],"mask-image-t-from-pos":[{"mask-t-from":cl()}],"mask-image-t-to-pos":[{"mask-t-to":cl()}],"mask-image-t-from-color":[{"mask-t-from":D()}],"mask-image-t-to-color":[{"mask-t-to":D()}],"mask-image-r-from-pos":[{"mask-r-from":cl()}],"mask-image-r-to-pos":[{"mask-r-to":cl()}],"mask-image-r-from-color":[{"mask-r-from":D()}],"mask-image-r-to-color":[{"mask-r-to":D()}],"mask-image-b-from-pos":[{"mask-b-from":cl()}],"mask-image-b-to-pos":[{"mask-b-to":cl()}],"mask-image-b-from-color":[{"mask-b-from":D()}],"mask-image-b-to-color":[{"mask-b-to":D()}],"mask-image-l-from-pos":[{"mask-l-from":cl()}],"mask-image-l-to-pos":[{"mask-l-to":cl()}],"mask-image-l-from-color":[{"mask-l-from":D()}],"mask-image-l-to-color":[{"mask-l-to":D()}],"mask-image-x-from-pos":[{"mask-x-from":cl()}],"mask-image-x-to-pos":[{"mask-x-to":cl()}],"mask-image-x-from-color":[{"mask-x-from":D()}],"mask-image-x-to-color":[{"mask-x-to":D()}],"mask-image-y-from-pos":[{"mask-y-from":cl()}],"mask-image-y-to-pos":[{"mask-y-to":cl()}],"mask-image-y-from-color":[{"mask-y-from":D()}],"mask-image-y-to-color":[{"mask-y-to":D()}],"mask-image-radial":[{"mask-radial":[G,Y]}],"mask-image-radial-from-pos":[{"mask-radial-from":cl()}],"mask-image-radial-to-pos":[{"mask-radial-to":cl()}],"mask-image-radial-from-color":[{"mask-radial-from":D()}],"mask-image-radial-to-color":[{"mask-radial-to":D()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":Yl()}],"mask-image-conic-pos":[{"mask-conic":[ll]}],"mask-image-conic-from-pos":[{"mask-conic-from":cl()}],"mask-image-conic-to-pos":[{"mask-conic-to":cl()}],"mask-image-conic-from-color":[{"mask-conic-from":D()}],"mask-image-conic-to-color":[{"mask-conic-to":D()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:rl()}],"mask-repeat":[{mask:o()}],"mask-size":[{mask:M()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,Y]}],filter:[{filter:["","none",G,Y]}],blur:[{blur:gl()}],brightness:[{brightness:[ll,G,Y]}],contrast:[{contrast:[ll,G,Y]}],"drop-shadow":[{"drop-shadow":["","none",xl,Fn,$n]}],"drop-shadow-color":[{"drop-shadow":D()}],grayscale:[{grayscale:["",ll,G,Y]}],"hue-rotate":[{"hue-rotate":[ll,G,Y]}],invert:[{invert:["",ll,G,Y]}],saturate:[{saturate:[ll,G,Y]}],sepia:[{sepia:["",ll,G,Y]}],"backdrop-filter":[{"backdrop-filter":["","none",G,Y]}],"backdrop-blur":[{"backdrop-blur":gl()}],"backdrop-brightness":[{"backdrop-brightness":[ll,G,Y]}],"backdrop-contrast":[{"backdrop-contrast":[ll,G,Y]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ll,G,Y]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ll,G,Y]}],"backdrop-invert":[{"backdrop-invert":["",ll,G,Y]}],"backdrop-opacity":[{"backdrop-opacity":[ll,G,Y]}],"backdrop-saturate":[{"backdrop-saturate":[ll,G,Y]}],"backdrop-sepia":[{"backdrop-sepia":["",ll,G,Y]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Q()}],"border-spacing-x":[{"border-spacing-x":Q()}],"border-spacing-y":[{"border-spacing-y":Q()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,Y]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ll,"initial",G,Y]}],ease:[{ease:["linear","initial",Zl,G,Y]}],delay:[{delay:[ll,G,Y]}],animate:[{animate:["none",Vl,G,Y]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[W,G,Y]}],"perspective-origin":[{"perspective-origin":P()}],rotate:[{rotate:at()}],"rotate-x":[{"rotate-x":at()}],"rotate-y":[{"rotate-y":at()}],"rotate-z":[{"rotate-z":at()}],scale:[{scale:Ft()}],"scale-x":[{"scale-x":Ft()}],"scale-y":[{"scale-y":Ft()}],"scale-z":[{"scale-z":Ft()}],"scale-3d":["scale-3d"],skew:[{skew:Pt()}],"skew-x":[{"skew-x":Pt()}],"skew-y":[{"skew-y":Pt()}],transform:[{transform:[G,Y,"","none","gpu","cpu"]}],"transform-origin":[{origin:P()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:It()}],"translate-x":[{"translate-x":It()}],"translate-y":[{"translate-y":It()}],"translate-z":[{"translate-z":It()}],"translate-none":["translate-none"],accent:[{accent:D()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:D()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,Y]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Q()}],"scroll-mx":[{"scroll-mx":Q()}],"scroll-my":[{"scroll-my":Q()}],"scroll-ms":[{"scroll-ms":Q()}],"scroll-me":[{"scroll-me":Q()}],"scroll-mt":[{"scroll-mt":Q()}],"scroll-mr":[{"scroll-mr":Q()}],"scroll-mb":[{"scroll-mb":Q()}],"scroll-ml":[{"scroll-ml":Q()}],"scroll-p":[{"scroll-p":Q()}],"scroll-px":[{"scroll-px":Q()}],"scroll-py":[{"scroll-py":Q()}],"scroll-ps":[{"scroll-ps":Q()}],"scroll-pe":[{"scroll-pe":Q()}],"scroll-pt":[{"scroll-pt":Q()}],"scroll-pr":[{"scroll-pr":Q()}],"scroll-pb":[{"scroll-pb":Q()}],"scroll-pl":[{"scroll-pl":Q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,Y]}],fill:[{fill:["none",...D()]}],"stroke-w":[{stroke:[ll,Hu,Ze,pf]}],stroke:[{stroke:["none",...D()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Cv=vv(jv);function Ve(...f){return Cv(Qd(f))}const qv=Zd("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function ju({className:f,variant:p,size:y,asChild:s=!1,...T}){const O=s?wd:"button";return C.jsx(O,{"data-slot":"button",className:Ve(qv({variant:p,size:y,className:f})),...T})}function Bv({className:f,type:p,...y}){return C.jsx("input",{type:p,"data-slot":"input",className:Ve("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",f),...y})}function Sf({className:f,...p}){return C.jsx("div",{"data-slot":"card",className:Ve("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",f),...p})}function Yv({className:f,...p}){return C.jsx("div",{"data-slot":"card-header",className:Ve("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",f),...p})}function Gv({className:f,...p}){return C.jsx("div",{"data-slot":"card-title",className:Ve("leading-none font-semibold",f),...p})}function xf({className:f,...p}){return C.jsx("div",{"data-slot":"card-content",className:Ve("px-6",f),...p})}const wv=Zd("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function Xv({className:f,variant:p,asChild:y=!1,...s}){const T=y?wd:"span";return C.jsx(T,{"data-slot":"badge",className:Ve(wv({variant:p}),f),...s})}function Qv(){const[f,p]=pl.useState(""),[y,s]=pl.useState("en"),[T,O]=pl.useState([]),[U,w]=pl.useState(!1),[_,S]=pl.useState(""),R={en:{title:"Book Information Retrieval System",searchPlaceholder:"Enter book name...",searchButton:"Search Books",categories:"Categories",downloadPdf:"Download PDF",publicDomain:"Public Domain - Free to Download",noResults:"No books found. Try a different search term.",error:"An error occurred while searching. Please try again.",loading:"Searching for books...",pdfLinks:"PDF Downloads",convert:"Convert",download:"Download"},ar:{title:"نظام استرجاع معلومات الكتب",searchPlaceholder:"أدخل اسم الكتاب...",searchButton:"البحث عن الكتب",categories:"التصنيفات",downloadPdf:"تحميل PDF",publicDomain:"ملكية عامة - مجاني للتحميل",noResults:"لم يتم العثور على كتب. جرب مصطلح بحث مختلف.",error:"حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.",loading:"البحث عن الكتب...",pdfLinks:"تحميلات PDF",convert:"تحويل",download:"تحميل"}}[y],J=async()=>{if(f.trim()){w(!0),S(""),O([]);try{const Z=await fetch("/api/books/enhanced-search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:f,language:y})});if(!Z.ok)throw new Error("Search failed");const W=await Z.json();O(W.results||[])}catch(Z){S("Failed to search books. Please try again."),console.error("Search error:",Z)}finally{w(!1)}}},$=async(Z,W)=>{try{const nl=await fetch("/api/books/convert-to-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:Z,format:W})});if(nl.ok){const Zl=await nl.blob(),Vl=window.URL.createObjectURL(Zl),ml=document.createElement("a");ml.href=Vl,ml.download="converted_book.pdf",document.body.appendChild(ml),ml.click(),window.URL.revokeObjectURL(Vl),document.body.removeChild(ml)}else alert("Conversion failed. Please try again.")}catch(nl){console.error("Conversion error:",nl),alert("Conversion failed. Please try again.")}},xl=()=>{s(y==="en"?"ar":"en")};return C.jsx("div",{className:`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 ${y==="ar"?"rtl":"ltr"}`,children:C.jsxs("div",{className:"container mx-auto px-4 py-8",children:[C.jsxs("div",{className:"text-center mb-8",children:[C.jsxs("div",{className:"flex justify-between items-center mb-4",children:[C.jsxs("div",{className:"flex items-center gap-2",children:[C.jsx(Nd,{className:"h-8 w-8 text-blue-600"}),C.jsx("span",{className:"text-xl font-bold text-blue-600",children:"BookFinder AI"})]}),C.jsxs(ju,{variant:"outline",size:"sm",onClick:xl,className:"flex items-center gap-2",children:[C.jsx(X0,{className:"h-4 w-4"}),y==="en"?"العربية":"English"]})]}),C.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:R.title}),C.jsx("p",{className:"text-lg text-gray-600",children:R.subtitle})]}),C.jsx(Sf,{className:"max-w-2xl mx-auto mb-8",children:C.jsx(xf,{className:"p-6",children:C.jsxs("div",{className:"flex gap-4",children:[C.jsx("div",{className:"flex-1",children:C.jsx(Bv,{type:"text",placeholder:R.searchPlaceholder,value:f,onChange:Z=>p(Z.target.value),onKeyPress:Z=>Z.key==="Enter"&&J(),className:"text-lg",dir:y==="ar"?"rtl":"ltr"})}),C.jsxs(ju,{onClick:J,disabled:U||!f.trim(),className:"px-6",children:[C.jsx(Z0,{className:"h-4 w-4 mr-2"}),R.searchButton]})]})})}),U&&C.jsxs("div",{className:"text-center py-8",children:[C.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),C.jsx("p",{className:"text-gray-600",children:R.loading})]}),_&&C.jsx("div",{className:"max-w-2xl mx-auto mb-8",children:C.jsx(Sf,{className:"border-red-200 bg-red-50",children:C.jsx(xf,{className:"p-4",children:C.jsx("p",{className:"text-red-600 text-center",children:_})})})}),T.length>0&&C.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:T.map(Z=>C.jsxs(Sf,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[C.jsx(Yv,{className:"pb-4",children:C.jsxs("div",{className:"flex gap-4",children:[C.jsx("img",{src:Z.cover_url||"/api/placeholder/120/180",alt:Z.title,className:"w-20 h-30 object-cover rounded",onError:W=>{W.target.src="/api/placeholder/120/180"}}),C.jsxs("div",{className:"flex-1",children:[C.jsx(Gv,{className:"text-lg mb-2 line-clamp-2",children:Z.title}),C.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600 mb-2",children:[C.jsx(k0,{className:"h-4 w-4"}),C.jsx("span",{children:Z.authors.join(", ")})]})]})]})}),C.jsx(xf,{children:C.jsxs("div",{className:"space-y-3",children:[C.jsxs("div",{children:[C.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[C.jsx(L0,{className:"h-4 w-4 text-gray-500"}),C.jsx("span",{className:"text-sm font-medium",children:R.categories})]}),C.jsx("div",{className:"flex flex-wrap gap-1",children:Z.categories.map((W,nl)=>C.jsx(Xv,{variant:"secondary",className:"text-xs",children:W},nl))})]}),Z.pdf_links&&Z.pdf_links.length>0?C.jsx("div",{className:"pt-2",children:C.jsx("div",{className:"space-y-2",children:Z.pdf_links.map((W,nl)=>C.jsxs("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded text-sm",children:[C.jsxs("span",{className:"text-gray-600",children:[W.source," ",W.type==="convertible"&&`(${W.format.toUpperCase()} → PDF)`]}),W.type==="convertible"?C.jsx(ju,{onClick:()=>$(W.url,W.format),size:"sm",variant:"outline",className:"text-xs",children:R.convert}):C.jsx(ju,{asChild:!0,size:"sm",className:"text-xs",children:C.jsx("a",{href:W.url,target:"_blank",rel:"noopener noreferrer",children:R.download})})]},nl))})}):Z.pdf_url&&C.jsxs("div",{className:"pt-2",children:[C.jsx(ju,{asChild:!0,className:"w-full",variant:Z.is_public_domain?"default":"outline",children:C.jsxs("a",{href:Z.pdf_url,target:"_blank",rel:"noopener noreferrer",children:[C.jsx(G0,{className:"h-4 w-4 mr-2"}),R.downloadPdf]})}),Z.is_public_domain&&C.jsx("p",{className:"text-xs text-green-600 mt-1 text-center",children:R.publicDomain})]})]})})]},Z.id))}),!U&&!_&&T.length===0&&f&&C.jsxs("div",{className:"text-center py-8",children:[C.jsx(Nd,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),C.jsx("p",{className:"text-gray-600",children:R.noResults})]})]})})}N0.createRoot(document.getElementById("root")).render(C.jsx(pl.StrictMode,{children:C.jsx(Qv,{})}));
